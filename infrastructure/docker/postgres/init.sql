-- 初始化试剂库存管理系统数据库
-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建数据库用户权限
GRANT ALL PRIVILEGES ON DATABASE haocai TO haocai_user;

-- 创建基础schema
CREATE SCHEMA IF NOT EXISTS public;

-- 设置搜索路径
ALTER DATABASE haocai SET search_path TO public;

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建审计日志函数
CREATE OR REPLACE FUNCTION create_audit_log()
RETURNS TRIGGER AS $$
BEGIN
    -- 这里可以添加审计日志逻辑
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

-- 初始化完成日志
INSERT INTO pg_stat_statements_info (dealloc) VALUES (0) ON CONFLICT DO NOTHING;
