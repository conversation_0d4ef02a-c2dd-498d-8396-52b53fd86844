# Next Steps

## UX Expert Prompt

@ux-expert 请基于此PRD创建用户体验架构，重点关注：

1. **移动端优化设计** - 为实验室现场使用场景设计专门的移动端界面和交互流程
2. **搜索体验设计** - 设计直观的搜索界面，支持18,000+试剂的快速查找和筛选
3. **数据可视化设计** - 为库存概览、使用趋势等数据分析功能设计清晰的可视化界面
4. **响应式设计规范** - 制定桌面、平板、手机三端的一致性设计规范和组件库

请使用 `*create-architecture` 命令开始UX架构设计。

## Architect Prompt

@architect 请基于此PRD创建技术架构，重点关注：

1. **高性能搜索架构** - 设计支持18,000+记录快速查询的搜索引擎架构（Elasticsearch集成）
2. **数据迁移架构** - 设计安全可靠的数据迁移方案，确保现有数据完整性
3. **移动端性能优化** - 设计PWA架构和缓存策略，支持离线查询和快速响应
4. **监控与运维架构** - 设计完整的系统监控、备份恢复和自动化运维方案

请使用 `*create-architecture` 命令开始技术架构设计。

---

**PRD创建完成！** 🎉

本文档为耗材库管理web应用提供了完整的产品需求定义，包括5个Epic和25个用户故事，已通过PM检查清单验证，准备进入架构设计阶段。
