# Technical Assumptions

## Repository Structure: Monorepo

采用Monorepo架构，将前端、后端、共享组件和文档统一管理在单一代码仓库中。这种结构特别适合耗材库管理系统，因为：
- 前后端代码紧密耦合，需要频繁的接口协调
- 团队规模适中（1-2名全栈开发人员），Monorepo便于代码共享和维护
- 简化部署流程，确保前后端版本一致性
- 便于统一的代码质量控制和CI/CD流程

## Service Architecture

**Monolith架构**：考虑到项目规模和团队资源，采用单体应用架构：
- **Web应用层**：React.js + Ant Design，提供响应式用户界面
- **API服务层**：Node.js + Express，处理业务逻辑和数据操作
- **数据存储层**：PostgreSQL主数据库 + Redis缓存
- **搜索引擎**：Elasticsearch，支持快速全文搜索和复杂筛选
- **文件存储**：本地文件系统，存储导入/导出文件和日志

选择Monolith而非微服务的原因：
- 项目复杂度适中，不需要微服务的额外复杂性
- 团队规模小，单体架构更易于开发和维护
- 部署和监控更简单，降低运维成本
- 数据一致性要求高，单体架构避免分布式事务问题

## Testing Requirements

**Unit + Integration测试策略**：
- **前端测试**：Jest + React Testing Library，覆盖组件逻辑和用户交互
- **后端测试**：Jest + Supertest，覆盖API接口和业务逻辑
- **集成测试**：Cypress，覆盖关键用户流程（搜索、库存操作、数据导入）
- **数据库测试**：使用测试数据库，确保数据操作的正确性
- **性能测试**：针对18,000+记录的查询性能进行专项测试

测试覆盖率目标：
- 单元测试覆盖率 ≥ 80%
- 集成测试覆盖核心业务流程
- 自动化测试集成到CI/CD流程中

## Additional Technical Assumptions and Requests

**开发环境和工具链**：
- **版本控制**：Git + GitHub/GitLab，支持代码审查和协作开发
- **包管理**：npm/yarn（前端）+ npm（后端），确保依赖版本一致性
- **构建工具**：Vite（前端构建）+ Docker（容器化部署）
- **代码质量**：ESLint + Prettier，统一代码风格和质量标准

**数据迁移和集成**：
- **数据导入**：支持Excel/CSV格式的批量数据导入，保持现有YF03/YF04/YF06编码体系
- **数据验证**：实施严格的数据验证规则，确保导入数据的完整性和准确性
- **增量同步**：过渡期间支持与现有系统的数据同步机制
- **备份策略**：自动化数据备份，支持定时备份和手动备份

**性能优化策略**：
- **数据库优化**：针对商品编码、名称、分类建立复合索引，优化查询性能
- **缓存策略**：Redis缓存常用查询结果和用户会话，减少数据库压力
- **前端优化**：代码分割、懒加载、图片压缩等技术提升页面加载速度
- **CDN部署**：静态资源CDN加速，提升用户访问体验

**安全和权限控制**：
- **身份认证**：JWT Token认证，支持用户登录状态管理
- **权限管理**：基于角色的访问控制（RBAC），支持管理员、操作员、查看者等角色
- **数据加密**：敏感数据加密存储，HTTPS传输加密
- **操作审计**：完整的操作日志记录，支持操作追溯和安全审计

**部署和运维**：
- **容器化部署**：Docker + Docker Compose，简化部署和环境管理
- **监控日志**：集成日志收集和监控系统，支持实时错误告警
- **自动化部署**：CI/CD流程自动化，支持测试环境和生产环境的自动部署
- **扩展性考虑**：架构设计支持水平扩展，为未来业务增长预留空间
