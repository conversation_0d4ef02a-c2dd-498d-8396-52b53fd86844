# Tech Stack

## Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| Frontend Language | TypeScript | 5.3+ | Type-safe frontend development | Prevents runtime errors, improves developer experience |
| Frontend Framework | Next.js | 14+ | React framework with SSR/SSG | Performance optimization, SEO, built-in routing |
| UI Component Library | Ant Design | 5.12+ | Professional UI components | Matches scientific/professional aesthetic, comprehensive components |
| State Management | Zustand | 4.4+ | Lightweight state management | Simple API, TypeScript support, less boilerplate than Redux |
| Backend Language | TypeScript | 5.3+ | Type-safe backend development | Shared types with frontend, better maintainability |
| Backend Framework | Express.js | 4.18+ | Web application framework | Mature ecosystem, middleware support, team familiarity |
| API Style | tRPC | 10.45+ | Type-safe API layer | End-to-end type safety, excellent DX, automatic client generation |
| Database | PostgreSQL | 15+ | Primary relational database | ACID compliance, JSON support, mature ecosystem |
| Search Engine | Elasticsearch | 8.11+ | Full-text search and analytics | High-performance search for 18K+ records, aggregations |
| Cache | Redis | 7.2+ | In-memory caching and sessions | Fast data access, session storage, real-time features |
| File Storage | Local FS | - | File uploads and exports | Simple deployment, no external dependencies |
| Authentication | NextAuth.js | 4.24+ | Authentication solution | Multiple providers, session management, security best practices |
| Frontend Testing | Vitest | 1.0+ | Unit testing framework | Fast, TypeScript support, Jest compatibility |
| Backend Testing | Jest | 29+ | Backend unit testing | Mature testing framework, extensive mocking capabilities |
| E2E Testing | Playwright | 1.40+ | End-to-end testing | Cross-browser testing, mobile testing, reliable selectors |
| Build Tool | Turborepo | 1.11+ | Monorepo build system | Fast builds, caching, parallel execution |
| Bundler | Webpack | 5+ (via Next.js) | Module bundling | Built into Next.js, optimized for React |
| IaC Tool | Docker Compose | 2.23+ | Container orchestration | Simple deployment, environment consistency |
| CI/CD | GitHub Actions | - | Continuous integration | Free for private repos, good ecosystem |
| Monitoring | Prometheus | 2.48+ | Metrics collection | Industry standard, extensive integrations |
| Logging | Winston | 3.11+ | Application logging | Structured logging, multiple transports |
| CSS Framework | Tailwind CSS | 3.3+ | Utility-first CSS | Rapid development, consistent design system |
