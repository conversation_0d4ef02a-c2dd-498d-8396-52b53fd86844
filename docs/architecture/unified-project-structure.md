# 统一项目结构 (Unified Project Structure)

## 概述

本文档定义了试剂库存管理系统的完整项目结构，采用 Monorepo 架构模式，确保代码组织的清晰性、可维护性和可扩展性。

## 架构原则

- **关注点分离**: 前端、后端、共享代码清晰分离
- **模块化设计**: 每个包都有明确的职责边界
- **类型安全**: 端到端的 TypeScript 类型安全
- **代码复用**: 通过共享包最大化代码复用
- **开发效率**: 优化的构建和开发工作流

## 完整项目结构

```
haocai/                         # 项目根目录
├── .github/                    # GitHub 配置和 CI/CD 工作流
│   ├── workflows/              # GitHub Actions 工作流
│   │   ├── ci.yml             # 持续集成：测试、构建、代码质量检查
│   │   ├── deploy.yml         # 自动化部署流程
│   │   ├── pr-check.yml       # Pull Request 检查
│   │   └── security-scan.yml  # 安全扫描
│   ├── ISSUE_TEMPLATE/         # Issue 模板
│   ├── PULL_REQUEST_TEMPLATE.md # PR 模板
│   └── dependabot.yml         # 依赖更新配置
├── apps/                       # 应用程序包
│   ├── web/                    # 前端应用 (Next.js 14+)
│   │   ├── src/
│   │   │   ├── components/     # React 组件
│   │   │   │   ├── ui/         # 基础 UI 组件 (Button, Input, etc.)
│   │   │   │   ├── forms/      # 表单组件 (ReagentForm, SearchForm)
│   │   │   │   ├── layout/     # 布局组件 (Header, Sidebar, Footer)
│   │   │   │   ├── features/   # 功能组件 (ReagentList, InventoryDashboard)
│   │   │   │   └── common/     # 通用组件 (Loading, ErrorBoundary)
│   │   │   ├── pages/          # Next.js 页面路由
│   │   │   │   ├── api/        # API 路由 (Next.js API routes)
│   │   │   │   ├── dashboard/  # 仪表板页面
│   │   │   │   ├── reagents/   # 试剂管理页面
│   │   │   │   ├── inventory/  # 库存管理页面
│   │   │   │   └── reports/    # 报表页面
│   │   │   ├── hooks/          # 自定义 React Hooks
│   │   │   │   ├── useReagents.ts    # 试剂相关 hooks
│   │   │   │   ├── useInventory.ts   # 库存相关 hooks
│   │   │   │   ├── useAuth.ts        # 认证相关 hooks
│   │   │   │   └── useLocalStorage.ts # 本地存储 hooks
│   │   │   ├── services/       # API 客户端服务
│   │   │   │   ├── api.ts      # tRPC 客户端配置
│   │   │   │   ├── reagents.ts # 试剂 API 服务
│   │   │   │   ├── inventory.ts # 库存 API 服务
│   │   │   │   └── auth.ts     # 认证 API 服务
│   │   │   ├── stores/         # Zustand 状态管理
│   │   │   │   ├── authStore.ts      # 用户认证状态
│   │   │   │   ├── reagentStore.ts   # 试剂数据状态
│   │   │   │   ├── inventoryStore.ts # 库存状态
│   │   │   │   └── uiStore.ts        # UI 状态 (主题、侧边栏等)
│   │   │   ├── styles/         # 样式文件
│   │   │   │   ├── globals.css # 全局样式
│   │   │   │   ├── components.css # 组件样式
│   │   │   │   └── themes/     # 主题配置
│   │   │   ├── utils/          # 前端工具函数
│   │   │   │   ├── format.ts   # 数据格式化
│   │   │   │   ├── validation.ts # 表单验证
│   │   │   │   ├── constants.ts # 前端常量
│   │   │   │   └── helpers.ts  # 通用辅助函数
│   │   │   └── types/          # 前端特定类型定义
│   │   ├── public/             # 静态资源
│   │   │   ├── images/         # 图片资源
│   │   │   ├── icons/          # 图标文件
│   │   │   ├── favicon.ico     # 网站图标
│   │   │   └── manifest.json   # PWA 配置
│   │   ├── tests/              # 前端测试
│   │   │   ├── __mocks__/      # Mock 文件
│   │   │   ├── components/     # 组件测试
│   │   │   ├── pages/          # 页面测试
│   │   │   ├── hooks/          # Hooks 测试
│   │   │   ├── utils/          # 工具函数测试
│   │   │   └── setup.ts        # 测试环境配置
│   │   ├── .env.local.example  # 环境变量示例
│   │   ├── next.config.js      # Next.js 配置
│   │   ├── tailwind.config.js  # Tailwind CSS 配置
│   │   ├── tsconfig.json       # TypeScript 配置
│   │   ├── vitest.config.ts    # Vitest 测试配置
│   │   └── package.json        # 前端依赖配置
│   └── api/                    # 后端应用 (Express + tRPC)
│       ├── src/
│       │   ├── routes/         # tRPC 路由定义
│       │   │   ├── reagent.ts  # 试剂相关路由
│       │   │   ├── inventory.ts # 库存相关路由
│       │   │   ├── user.ts     # 用户相关路由
│       │   │   ├── analytics.ts # 分析报表路由
│       │   │   └── index.ts    # 路由聚合
│       │   ├── services/       # 业务逻辑服务
│       │   │   ├── ReagentService.ts    # 试剂业务逻辑
│       │   │   ├── InventoryService.ts  # 库存业务逻辑
│       │   │   ├── UserService.ts       # 用户业务逻辑
│       │   │   ├── SearchService.ts     # 搜索业务逻辑
│       │   │   ├── AnalyticsService.ts  # 分析业务逻辑
│       │   │   └── EmailService.ts      # 邮件服务
│       │   ├── models/         # 数据模型和仓储层
│       │   │   ├── ReagentRepository.ts # 试剂数据访问
│       │   │   ├── InventoryRepository.ts # 库存数据访问
│       │   │   ├── UserRepository.ts    # 用户数据访问
│       │   │   └── BaseRepository.ts    # 基础仓储类
│       │   ├── middleware/     # Express 中间件
│       │   │   ├── auth.ts     # 认证中间件
│       │   │   ├── cors.ts     # CORS 配置
│       │   │   ├── logging.ts  # 日志中间件
│       │   │   ├── rateLimit.ts # 限流中间件
│       │   │   ├── validation.ts # 请求验证
│       │   │   └── errorHandler.ts # 错误处理
│       │   ├── utils/          # 后端工具函数
│       │   │   ├── database.ts # 数据库连接工具
│       │   │   ├── logger.ts   # 日志工具
│       │   │   ├── cache.ts    # 缓存工具
│       │   │   ├── email.ts    # 邮件工具
│       │   │   ├── crypto.ts   # 加密工具
│       │   │   └── constants.ts # 后端常量
│       │   ├── config/         # 配置文件
│       │   │   ├── database.ts # 数据库配置
│       │   │   ├── redis.ts    # Redis 配置
│       │   │   ├── auth.ts     # 认证配置
│       │   │   └── app.ts      # 应用配置
│       │   ├── types/          # 后端类型定义
│       │   │   ├── api.ts      # API 类型
│       │   │   ├── database.ts # 数据库类型
│       │   │   └── auth.ts     # 认证类型
│       │   └── server.ts       # Express 服务器入口
│       ├── tests/              # 后端测试
│       │   ├── __mocks__/      # Mock 文件
│       │   ├── unit/           # 单元测试
│       │   │   ├── services/   # 服务层测试
│       │   │   ├── models/     # 模型层测试
│       │   │   └── utils/      # 工具函数测试
│       │   ├── integration/    # 集成测试
│       │   │   ├── routes/     # 路由测试
│       │   │   └── database/   # 数据库测试
│       │   ├── fixtures/       # 测试数据
│       │   ├── setup.ts        # 测试环境配置
│       │   └── teardown.ts     # 测试清理
│       ├── prisma/             # 数据库 Schema 和迁移
│       │   ├── schema.prisma   # Prisma Schema 定义
│       │   ├── migrations/     # 数据库迁移文件
│       │   ├── seeds/          # 种子数据
│       │   │   ├── dev.ts      # 开发环境种子数据
│       │   │   ├── test.ts     # 测试环境种子数据
│       │   │   └── prod.ts     # 生产环境种子数据
│       │   └── seed.ts         # 种子数据入口
│       ├── .env.example        # 环境变量示例
│       ├── tsconfig.json       # TypeScript 配置
│       ├── jest.config.js      # Jest 测试配置
│       └── package.json        # 后端依赖配置
├── packages/                   # 共享包
│   ├── shared/                 # 共享类型和工具
│   │   ├── src/
│   │   │   ├── types/          # 共享 TypeScript 接口
│   │   │   │   ├── reagent.ts  # 试剂相关类型
│   │   │   │   ├── inventory.ts # 库存相关类型
│   │   │   │   ├── user.ts     # 用户相关类型
│   │   │   │   ├── api.ts      # API 响应类型
│   │   │   │   └── common.ts   # 通用类型
│   │   │   ├── constants/      # 共享常量
│   │   │   │   ├── reagent.ts  # 试剂相关常量
│   │   │   │   ├── inventory.ts # 库存相关常量
│   │   │   │   ├── api.ts      # API 相关常量
│   │   │   │   └── app.ts      # 应用常量
│   │   │   ├── utils/          # 共享工具函数
│   │   │   │   ├── validation.ts # 数据验证工具
│   │   │   │   ├── format.ts   # 格式化工具
│   │   │   │   ├── date.ts     # 日期处理工具
│   │   │   │   ├── string.ts   # 字符串处理工具
│   │   │   │   └── math.ts     # 数学计算工具
│   │   │   ├── schemas/        # Zod 验证 Schema
│   │   │   │   ├── reagent.ts  # 试剂验证 Schema
│   │   │   │   ├── inventory.ts # 库存验证 Schema
│   │   │   │   ├── user.ts     # 用户验证 Schema
│   │   │   │   └── common.ts   # 通用验证 Schema
│   │   │   └── index.ts        # 导出入口
│   │   ├── tests/              # 共享包测试
│   │   ├── tsconfig.json       # TypeScript 配置
│   │   └── package.json        # 共享包依赖
│   ├── ui/                     # 共享 UI 组件库
│   │   ├── src/
│   │   │   ├── components/     # 可复用 UI 组件
│   │   │   │   ├── Button/     # 按钮组件
│   │   │   │   │   ├── Button.tsx
│   │   │   │   │   ├── Button.test.tsx
│   │   │   │   │   ├── Button.stories.tsx
│   │   │   │   │   └── index.ts
│   │   │   │   ├── Input/      # 输入框组件
│   │   │   │   ├── Modal/      # 模态框组件
│   │   │   │   ├── Table/      # 表格组件
│   │   │   │   ├── Form/       # 表单组件
│   │   │   │   └── Chart/      # 图表组件
│   │   │   ├── hooks/          # 共享 React Hooks
│   │   │   │   ├── useDebounce.ts
│   │   │   │   ├── useLocalStorage.ts
│   │   │   │   └── useMediaQuery.ts
│   │   │   ├── utils/          # UI 工具函数
│   │   │   │   ├── cn.ts       # className 合并工具
│   │   │   │   ├── theme.ts    # 主题工具
│   │   │   │   └── responsive.ts # 响应式工具
│   │   │   ├── styles/         # 共享样式
│   │   │   │   ├── globals.css
│   │   │   │   ├── components.css
│   │   │   │   └── variables.css
│   │   │   └── index.ts        # 组件库导出
│   │   ├── tests/              # UI 组件测试
│   │   ├── storybook/          # Storybook 配置
│   │   ├── tsconfig.json       # TypeScript 配置
│   │   ├── vitest.config.ts    # 测试配置
│   │   └── package.json        # UI 包依赖
│   └── config/                 # 共享配置包
│       ├── eslint/             # ESLint 配置
│       │   ├── base.js         # 基础 ESLint 配置
│       │   ├── react.js        # React ESLint 配置
│       │   ├── node.js         # Node.js ESLint 配置
│       │   └── typescript.js   # TypeScript ESLint 配置
│       ├── typescript/         # TypeScript 配置
│       │   ├── base.json       # 基础 TS 配置
│       │   ├── react.json      # React TS 配置
│       │   ├── node.json       # Node.js TS 配置
│       │   └── strict.json     # 严格模式 TS 配置
│       ├── jest/               # Jest 测试配置
│       │   ├── base.js         # 基础 Jest 配置
│       │   ├── react.js        # React Jest 配置
│       │   └── node.js         # Node.js Jest 配置
│       ├── prettier/           # Prettier 配置
│       │   └── index.js        # Prettier 规则
│       ├── tailwind/           # Tailwind CSS 配置
│       │   ├── base.js         # 基础 Tailwind 配置
│       │   └── components.js   # 组件 Tailwind 配置
│       └── package.json        # 配置包依赖
├── infrastructure/             # 基础设施和部署
│   ├── docker/                 # Docker 配置
│   │   ├── Dockerfile.web      # 前端 Docker 镜像
│   │   ├── Dockerfile.api      # 后端 Docker 镜像
│   │   ├── Dockerfile.nginx    # Nginx Docker 镜像
│   │   ├── docker-compose.yml  # 生产环境 Docker Compose
│   │   ├── docker-compose.dev.yml # 开发环境 Docker Compose
│   │   └── docker-compose.test.yml # 测试环境 Docker Compose
│   ├── nginx/                  # Nginx 配置
│   │   ├── nginx.conf          # 主配置文件
│   │   ├── sites-available/    # 站点配置
│   │   └── ssl/                # SSL 证书
│   ├── kubernetes/             # Kubernetes 配置 (可选)
│   │   ├── namespace.yaml
│   │   ├── deployment.yaml
│   │   ├── service.yaml
│   │   └── ingress.yaml
│   └── terraform/              # Terraform 基础设施代码 (可选)
│       ├── main.tf
│       ├── variables.tf
│       └── outputs.tf
├── scripts/                    # 构建和部署脚本
│   ├── build.sh                # 构建脚本
│   ├── deploy.sh               # 部署脚本
│   ├── migrate.sh              # 数据库迁移脚本
│   ├── backup.sh               # 数据备份脚本
│   ├── restore.sh              # 数据恢复脚本
│   ├── seed.sh                 # 种子数据脚本
│   ├── test.sh                 # 测试运行脚本
│   └── clean.sh                # 清理脚本
├── tests/                      # 端到端测试
│   ├── e2e/                    # Playwright E2E 测试
│   │   ├── specs/              # 测试规范
│   │   │   ├── auth.spec.ts    # 认证流程测试
│   │   │   ├── reagent.spec.ts # 试剂管理测试
│   │   │   ├── inventory.spec.ts # 库存管理测试
│   │   │   └── search.spec.ts  # 搜索功能测试
│   │   ├── fixtures/           # 测试数据
│   │   ├── pages/              # Page Object Model
│   │   └── utils/              # 测试工具
│   ├── performance/            # 性能测试
│   │   ├── load-test.js        # 负载测试
│   │   └── stress-test.js      # 压力测试
│   ├── security/               # 安全测试
│   │   └── security-scan.js    # 安全扫描
│   ├── playwright.config.ts    # Playwright 配置
│   └── package.json            # 测试依赖
├── docs/                       # 项目文档
│   ├── architecture/           # 架构文档
│   │   ├── index.md            # 架构概览
│   │   ├── high-level-architecture.md # 高层架构
│   │   ├── backend-architecture.md # 后端架构
│   │   ├── frontend-architecture.md # 前端架构
│   │   ├── database-schema.md  # 数据库设计
│   │   ├── api-specification.md # API 规范
│   │   ├── deployment-architecture.md # 部署架构
│   │   ├── tech-stack.md       # 技术栈
│   │   ├── unified-project-structure.md # 项目结构
│   │   └── components.md       # 组件设计
│   ├── prd/                    # 产品需求文档
│   │   ├── index.md            # PRD 概览
│   │   ├── requirements.md     # 功能需求
│   │   ├── goals-and-background-context.md # 目标和背景
│   │   ├── technical-assumptions.md # 技术假设
│   │   ├── epic-list.md        # Epic 列表
│   │   ├── epic-1-基础架构与核心搜索功能.md
│   │   ├── epic-2-库存管理与数据操作.md
│   │   ├── epic-3-数据迁移与系统集成.md
│   │   ├── epic-4-用户体验优化与移动端支持.md
│   │   ├── epic-5-权限管理与系统监控.md
│   │   └── next-steps.md       # 下一步计划
│   ├── stories/                # 用户故事
│   │   ├── 1.1.story.md        # 项目基础设施搭建
│   │   └── 1.2.story.md        # 试剂数据模型设计
│   ├── api/                    # API 文档
│   │   ├── README.md           # API 概览
│   │   ├── authentication.md   # 认证文档
│   │   ├── reagents.md         # 试剂 API
│   │   ├── inventory.md        # 库存 API
│   │   └── analytics.md        # 分析 API
│   ├── deployment/             # 部署文档
│   │   ├── README.md           # 部署概览
│   │   ├── development.md      # 开发环境部署
│   │   ├── staging.md          # 测试环境部署
│   │   ├── production.md       # 生产环境部署
│   │   └── troubleshooting.md  # 故障排除
│   ├── coding-standards.md     # 编码规范
│   ├── brief.md                # 项目简介
│   ├── prd.md                  # 产品需求文档
│   ├── front-end-spec.md       # 前端规范
│   └── architecture.md         # 架构文档
├── .env.example                # 环境变量模板
├── .gitignore                  # Git 忽略文件
├── .gitattributes              # Git 属性配置
├── .editorconfig               # 编辑器配置
├── .nvmrc                      # Node.js 版本配置
├── package.json                # 根 package.json (Monorepo 配置)
├── package-lock.json           # 依赖锁定文件
├── turbo.json                  # Turborepo 配置
├── tsconfig.json               # 根 TypeScript 配置
├── README.md                   # 项目说明文档
├── CHANGELOG.md                # 变更日志
├── CONTRIBUTING.md             # 贡献指南
└── LICENSE                     # 开源许可证
```

## 目录职责说明

### 应用层 (`apps/`)

#### 前端应用 (`apps/web/`)
- **技术栈**: Next.js 14+ + TypeScript + Ant Design + Tailwind CSS
- **职责**: 用户界面、用户交互、状态管理、API 调用
- **特点**:
  - 响应式设计，支持桌面和移动端
  - PWA 支持，提供离线功能
  - 服务端渲染 (SSR) 优化性能
  - 组件化开发，提高代码复用性

#### 后端应用 (`apps/api/`)
- **技术栈**: Express.js + tRPC + TypeScript + Prisma + PostgreSQL
- **职责**: 业务逻辑、数据处理、API 服务、认证授权
- **特点**:
  - 类型安全的 API 层 (tRPC)
  - 分层架构 (Controller → Service → Repository)
  - 数据库 ORM (Prisma)
  - 中间件支持 (认证、日志、限流等)

### 共享包层 (`packages/`)

#### 共享类型和工具 (`packages/shared/`)
- **职责**: 前后端共享的类型定义、工具函数、常量、验证规则
- **优势**: 确保前后端类型一致性，减少重复代码
- **内容**: TypeScript 接口、Zod 验证 Schema、工具函数

#### UI 组件库 (`packages/ui/`)
- **职责**: 可复用的 UI 组件、设计系统、主题配置
- **优势**: 统一的视觉风格，提高开发效率
- **内容**: 基础组件、复合组件、Hooks、样式

#### 配置包 (`packages/config/`)
- **职责**: 共享的开发工具配置
- **优势**: 统一的代码规范和构建配置
- **内容**: ESLint、TypeScript、Jest、Prettier 配置

### 基础设施层 (`infrastructure/`)

#### Docker 容器化
- **开发环境**: 快速启动本地开发环境
- **生产环境**: 容器化部署，确保环境一致性
- **服务编排**: Docker Compose 管理多服务

#### Nginx 反向代理
- **负载均衡**: 分发请求到多个后端实例
- **静态资源**: 高效服务静态文件
- **SSL 终止**: HTTPS 加密和证书管理

### 测试策略

#### 单元测试
- **前端**: Vitest + React Testing Library
- **后端**: Jest + Supertest
- **覆盖率**: 目标 80% 以上

#### 集成测试
- **API 测试**: 测试 tRPC 路由和业务逻辑
- **数据库测试**: 测试数据访问层

#### 端到端测试
- **工具**: Playwright
- **范围**: 关键用户流程 (搜索、库存操作、数据导入)

## 开发工作流

### 本地开发环境设置

```bash
# 1. 克隆项目
git clone <repository-url>
cd haocai

# 2. 安装依赖
npm install

# 3. 设置环境变量
cp .env.example .env
# 编辑 .env 文件配置数据库等信息

# 4. 启动开发数据库
docker-compose -f infrastructure/docker/docker-compose.dev.yml up -d

# 5. 运行数据库迁移
cd apps/api && npx prisma migrate dev

# 6. 种子数据
npx prisma db seed

# 7. 启动开发服务器
npm run dev
```

### 开发命令

```bash
# 启动所有服务
npm run dev

# 启动特定应用
npm run dev --filter=web    # 仅前端
npm run dev --filter=api    # 仅后端

# 构建项目
npm run build

# 运行测试
npm run test                # 所有测试
npm run test:coverage       # 测试覆盖率

# 代码质量检查
npm run lint                # ESLint 检查
npm run format              # Prettier 格式化
npm run type-check          # TypeScript 类型检查

# 数据库操作
npm run db:generate         # 生成 Prisma 客户端
npm run db:push             # 推送 Schema 到数据库
npm run db:migrate          # 运行数据库迁移

# Docker 操作
npm run docker:up           # 启动 Docker 服务
npm run docker:down         # 停止 Docker 服务
npm run docker:logs         # 查看 Docker 日志
```

### 分支策略

```bash
# 主分支
main                        # 生产环境代码
develop                     # 开发环境代码

# 功能分支
feature/reagent-search      # 新功能开发
feature/mobile-ui           # 移动端界面

# 修复分支
fix/inventory-bug           # Bug 修复
hotfix/security-patch       # 紧急修复

# 发布分支
release/v1.0.0              # 版本发布
```

## 扩展指南

### 添加新功能模块

1. **创建共享类型** (`packages/shared/src/types/`)
2. **定义验证 Schema** (`packages/shared/src/schemas/`)
3. **实现后端 API** (`apps/api/src/routes/`)
4. **创建前端组件** (`apps/web/src/components/`)
5. **添加页面路由** (`apps/web/src/pages/`)
6. **编写测试用例**

### 添加新的共享组件

1. **创建组件** (`packages/ui/src/components/`)
2. **编写测试** (`packages/ui/tests/`)
3. **添加 Storybook 故事** (`packages/ui/storybook/`)
4. **更新导出** (`packages/ui/src/index.ts`)

### 数据库 Schema 变更

1. **修改 Prisma Schema** (`apps/api/prisma/schema.prisma`)
2. **生成迁移** (`npx prisma migrate dev`)
3. **更新类型定义** (`packages/shared/src/types/`)
4. **更新种子数据** (`apps/api/prisma/seeds/`)

## 性能优化策略

### 前端优化
- **代码分割**: 路由级别的懒加载
- **组件优化**: React.memo、useMemo、useCallback
- **资源优化**: 图片压缩、字体优化
- **缓存策略**: SWR 数据缓存、浏览器缓存

### 后端优化
- **数据库优化**: 索引优化、查询优化
- **缓存策略**: Redis 缓存热点数据
- **API 优化**: 分页、字段选择、批量操作
- **连接池**: 数据库连接池管理

### 构建优化
- **Turborepo**: 增量构建和缓存
- **并行构建**: 多包并行构建
- **依赖优化**: 按需导入、Tree Shaking

## 安全考虑

### 认证和授权
- **JWT Token**: 无状态认证
- **RBAC**: 基于角色的访问控制
- **权限中间件**: API 级别的权限验证

### 数据安全
- **输入验证**: Zod Schema 验证
- **SQL 注入防护**: Prisma ORM 参数化查询
- **XSS 防护**: 输出转义、CSP 头
- **CSRF 防护**: CSRF Token 验证

### 网络安全
- **HTTPS**: 强制 HTTPS 连接
- **CORS**: 跨域请求控制
- **限流**: API 请求频率限制
- **安全头**: 安全相关的 HTTP 头

---

**这个统一的项目结构确保了代码的组织性、可维护性和可扩展性，为试剂库存管理系统的长期发展奠定了坚实的基础。**
