# Components

## Frontend Application (Next.js)

**Responsibility:** User interface, client-side logic, and user experience optimization

**Key Interfaces:**
- tRPC client for type-safe API communication
- PWA service worker for offline capabilities
- Real-time WebSocket connections for live updates
- Local storage for user preferences and offline data

**Dependencies:** Backend API, authentication service, CDN for assets

**Technology Stack:** Next.js 14, React 18, TypeScript, Ant Design, Tailwind CSS, Zustand

## Backend API Server (Express + tRPC)

**Responsibility:** Business logic, data validation, authentication, and API endpoints

**Key Interfaces:**
- tRPC routers for type-safe API endpoints
- Database connections (PostgreSQL, Elasticsearch, Redis)
- File upload/download handling
- WebSocket server for real-time features

**Dependencies:** PostgreSQL database, Elasticsearch cluster, Redis cache, file storage

**Technology Stack:** Express.js, tRPC, TypeScript, Prisma ORM, Winston logging

## Search Engine (Elasticsearch)

**Responsibility:** High-performance full-text search, aggregations, and analytics

**Key Interfaces:**
- REST API for search queries
- Bulk indexing for data synchronization
- Aggregation APIs for analytics

**Dependencies:** PostgreSQL for data source, API server for queries

**Technology Stack:** Elasticsearch 8.11, custom analyzers for Chinese text

## Database Layer (PostgreSQL)

**Responsibility:** Primary data storage, ACID transactions, and data integrity

**Key Interfaces:**
- SQL queries via Prisma ORM
- Connection pooling for performance
- Backup and replication interfaces

**Dependencies:** File system for storage, backup storage

**Technology Stack:** PostgreSQL 15, Prisma ORM, pg_dump for backups

## Cache Layer (Redis)

**Responsibility:** Session storage, query result caching, and real-time data

**Key Interfaces:**
- Key-value storage for sessions
- Pub/Sub for real-time notifications
- Cache invalidation strategies

**Dependencies:** API server for cache management

**Technology Stack:** Redis 7.2, Redis Sentinel for high availability

## File Storage Service

**Responsibility:** Handle file uploads, exports, and document management

**Key Interfaces:**
- File upload API endpoints
- Export generation (Excel, CSV)
- Static file serving

**Dependencies:** Local file system, backup storage

**Technology Stack:** Multer for uploads, ExcelJS for exports, local filesystem

## Component Diagrams

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[React Components]
        STATE[Zustand Store]
        TRPC_CLIENT[tRPC Client]
        SW[Service Worker]
    end

    subgraph "API Layer"
        TRPC_SERVER[tRPC Server]
        AUTH[Auth Middleware]
        VALID[Validation Layer]
    end

    subgraph "Business Logic"
        REAGENT_SVC[Reagent Service]
        STOCK_SVC[Stock Service]
        USER_SVC[User Service]
        ALERT_SVC[Alert Service]
    end

    subgraph "Data Access"
        PRISMA[Prisma ORM]
        ES_CLIENT[Elasticsearch Client]
        REDIS_CLIENT[Redis Client]
    end

    subgraph "Storage"
        PG[(PostgreSQL)]
        ES[(Elasticsearch)]
        REDIS[(Redis)]
        FILES[File Storage]
    end

    UI --> STATE
    STATE --> TRPC_CLIENT
    TRPC_CLIENT --> TRPC_SERVER

    TRPC_SERVER --> AUTH
    AUTH --> VALID
    VALID --> REAGENT_SVC
    VALID --> STOCK_SVC
    VALID --> USER_SVC
    VALID --> ALERT_SVC

    REAGENT_SVC --> PRISMA
    REAGENT_SVC --> ES_CLIENT
    STOCK_SVC --> PRISMA
    STOCK_SVC --> REDIS_CLIENT
    USER_SVC --> PRISMA
    ALERT_SVC --> PRISMA

    PRISMA --> PG
    ES_CLIENT --> ES
    REDIS_CLIENT --> REDIS
    REAGENT_SVC --> FILES
```
