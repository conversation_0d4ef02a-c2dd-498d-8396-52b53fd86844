# Data Models

## Reagent

**Purpose:** Core entity representing laboratory reagents and consumables with complete inventory tracking

**Key Attributes:**
- id: string (UUID) - Unique identifier
- code: string - YF03/YF04/YF06 classification code
- name: string - Reagent name (e.g., "亘诺细胞冻存液")
- specification: string - Size/volume specification (e.g., "50ml", "10ml")
- supplier: string - Supplier/manufacturer name
- category: ReagentCategory - Classification enum
- unit: string - Measurement unit
- currentStock: number - Current available quantity
- minThreshold: number - Low stock warning threshold
- maxCapacity: number - Maximum storage capacity
- storageCondition: string - Storage requirements (e.g., "-80°C", "4°C")
- location: string - Physical storage location
- isActive: boolean - Whether reagent is currently in use

### TypeScript Interface

```typescript
interface Reagent {
  id: string;
  code: string;
  name: string;
  specification: string;
  supplier: string;
  category: ReagentCategory;
  unit: string;
  currentStock: number;
  minThreshold: number;
  maxCapacity: number;
  storageCondition: string;
  location: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

enum ReagentCategory {
  BIOLOGICAL_REAGENT = 'YF03',
  LAB_CONSUMABLE = 'YF04',
  CULTURE_MEDIUM = 'YF06'
}
```

### Relationships
- One-to-many with StockTransaction (inventory movements)
- One-to-many with StockAlert (low stock warnings)
- Many-to-one with User (created by, updated by)

## StockTransaction

**Purpose:** Track all inventory movements (in/out/adjustments) with full audit trail

**Key Attributes:**
- id: string (UUID) - Unique transaction identifier
- reagentId: string - Reference to reagent
- type: TransactionType - Type of transaction
- quantity: number - Amount moved (positive for in, negative for out)
- previousStock: number - Stock level before transaction
- newStock: number - Stock level after transaction
- reason: string - Purpose or reason for transaction
- batchNumber: string - Batch/lot number if applicable
- expiryDate: Date - Expiry date for batch
- projectCode: string - Associated research project
- userId: string - User who performed transaction
- notes: string - Additional notes

### TypeScript Interface

```typescript
interface StockTransaction {
  id: string;
  reagentId: string;
  type: TransactionType;
  quantity: number;
  previousStock: number;
  newStock: number;
  reason: string;
  batchNumber?: string;
  expiryDate?: Date;
  projectCode?: string;
  userId: string;
  notes?: string;
  createdAt: Date;
}

enum TransactionType {
  STOCK_IN = 'STOCK_IN',
  STOCK_OUT = 'STOCK_OUT',
  ADJUSTMENT = 'ADJUSTMENT',
  TRANSFER = 'TRANSFER'
}
```

### Relationships
- Many-to-one with Reagent (transaction belongs to reagent)
- Many-to-one with User (transaction performed by user)

## User

**Purpose:** System users with role-based access control and activity tracking

**Key Attributes:**
- id: string (UUID) - Unique user identifier
- email: string - Login email address
- name: string - Full name
- role: UserRole - Access level and permissions
- department: string - Research department/team
- isActive: boolean - Account status
- lastLoginAt: Date - Last login timestamp
- preferences: UserPreferences - Personal settings

### TypeScript Interface

```typescript
interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  department: string;
  isActive: boolean;
  lastLoginAt?: Date;
  preferences: UserPreferences;
  createdAt: Date;
  updatedAt: Date;
}

enum UserRole {
  ADMIN = 'ADMIN',
  MANAGER = 'MANAGER',
  OPERATOR = 'OPERATOR',
  VIEWER = 'VIEWER'
}

interface UserPreferences {
  theme: 'light' | 'dark';
  language: 'zh-CN' | 'en-US';
  defaultView: 'list' | 'grid';
  itemsPerPage: number;
  favoriteReagents: string[];
}
```

### Relationships
- One-to-many with StockTransaction (user performs transactions)
- One-to-many with StockAlert (user receives alerts)

## StockAlert

**Purpose:** Automated alerts for low stock, expiry warnings, and system notifications

**Key Attributes:**
- id: string (UUID) - Unique alert identifier
- reagentId: string - Related reagent
- type: AlertType - Type of alert
- severity: AlertSeverity - Priority level
- message: string - Alert description
- isRead: boolean - Whether user has seen alert
- isResolved: boolean - Whether issue is resolved
- userId: string - Target user for alert
- triggeredAt: Date - When alert was created
- resolvedAt: Date - When alert was resolved

### TypeScript Interface

```typescript
interface StockAlert {
  id: string;
  reagentId?: string;
  type: AlertType;
  severity: AlertSeverity;
  message: string;
  isRead: boolean;
  isResolved: boolean;
  userId: string;
  triggeredAt: Date;
  resolvedAt?: Date;
}

enum AlertType {
  LOW_STOCK = 'LOW_STOCK',
  OUT_OF_STOCK = 'OUT_OF_STOCK',
  EXPIRY_WARNING = 'EXPIRY_WARNING',
  SYSTEM_ALERT = 'SYSTEM_ALERT'
}

enum AlertSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}
```

### Relationships
- Many-to-one with Reagent (alert relates to reagent)
- Many-to-one with User (alert targets user)
