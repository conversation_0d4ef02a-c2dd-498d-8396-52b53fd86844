# Core Workflows

## Reagent Search and View Workflow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant tRPC
    participant API
    participant ES as Elasticsearch
    participant PG as PostgreSQL
    participant Cache as Redis

    User->>Frontend: Enter search query
    Frontend->>tRPC: reagent.search(query, filters)
    tRPC->>API: Validate and process request
    API->>Cache: Check cached results

    alt Cache Hit
        Cache-->>API: Return cached data
    else Cache Miss
        API->>ES: Execute search query
        ES-->>API: Return search results
        API->>PG: Get additional reagent details
        PG-->>API: Return complete data
        API->>Cache: Store results in cache
    end

    API-->>tRPC: Return search results
    tRPC-->>Frontend: Type-safe response
    Frontend-->>User: Display reagent list

    User->>Frontend: Click reagent for details
    Frontend->>tRPC: reagent.getById(id)
    tRPC->>API: Get reagent details
    API->>PG: Query reagent and related data
    PG-->>API: Return complete reagent info
    API-->>tRPC: Return reagent details
    tRPC-->>Frontend: Type-safe response
    Frontend-->>User: Display reagent details
```

## Stock Transaction Workflow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant tRPC
    participant API
    participant PG as PostgreSQL
    participant ES as Elasticsearch
    participant Alert as Alert Service

    User->>Frontend: Initiate stock out
    Frontend->>tRPC: stock.stockOut(reagentId, quantity, reason)
    tRPC->>API: Validate request

    API->>PG: Begin transaction
    API->>PG: Check current stock

    alt Sufficient Stock
        API->>PG: Create stock transaction
        API->>PG: Update reagent stock
        API->>PG: Commit transaction

        API->>ES: Update search index
        API->>Alert: Check for low stock alerts

        alt Stock Below Threshold
            Alert->>PG: Create low stock alert
            Alert->>Frontend: Send real-time notification
        end

        API-->>tRPC: Success response
        tRPC-->>Frontend: Transaction confirmed
        Frontend-->>User: Show success message

    else Insufficient Stock
        API->>PG: Rollback transaction
        API-->>tRPC: Error response
        tRPC-->>Frontend: Stock insufficient error
        Frontend-->>User: Show error message
    end
```

## Data Import Workflow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant tRPC
    participant API
    participant FileService
    participant PG as PostgreSQL
    participant ES as Elasticsearch
    participant Validator

    User->>Frontend: Upload Excel file
    Frontend->>tRPC: data.import(file)
    tRPC->>API: Process import request
    API->>FileService: Save uploaded file

    API->>FileService: Parse Excel data
    FileService-->>API: Return parsed rows

    API->>Validator: Validate each row
    Validator-->>API: Return validation results

    alt All Valid
        API->>PG: Begin bulk transaction
        loop For each reagent
            API->>PG: Insert/update reagent
        end
        API->>PG: Commit transaction

        API->>ES: Bulk index update
        API-->>tRPC: Success with summary
        tRPC-->>Frontend: Import completed
        Frontend-->>User: Show success summary

    else Validation Errors
        API-->>tRPC: Error with details
        tRPC-->>Frontend: Validation errors
        Frontend-->>User: Show error report
    end
```
