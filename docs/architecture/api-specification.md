# API Specification

## tRPC Router Definitions

```typescript
import { z } from 'zod';
import { router, publicProcedure, protectedProcedure } from '../trpc';

// Reagent Router
export const reagentRouter = router({
  // Search reagents with filters
  search: publicProcedure
    .input(z.object({
      query: z.string().optional(),
      category: z.nativeEnum(ReagentCategory).optional(),
      supplier: z.string().optional(),
      lowStock: z.boolean().optional(),
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ input, ctx }) => {
      // Implementation with Elasticsearch integration
    }),

  // Get reagent by ID
  getById: publicProcedure
    .input(z.string().uuid())
    .query(async ({ input, ctx }) => {
      // Implementation
    }),

  // Create new reagent (admin/manager only)
  create: protectedProcedure
    .input(z.object({
      code: z.string(),
      name: z.string(),
      specification: z.string(),
      supplier: z.string(),
      category: z.nativeEnum(ReagentCategory),
      unit: z.string(),
      minThreshold: z.number().min(0),
      maxCapacity: z.number().min(0),
      storageCondition: z.string(),
      location: z.string(),
    }))
    .mutation(async ({ input, ctx }) => {
      // Implementation with audit logging
    }),

  // Update reagent
  update: protectedProcedure
    .input(z.object({
      id: z.string().uuid(),
      data: z.object({
        name: z.string().optional(),
        specification: z.string().optional(),
        supplier: z.string().optional(),
        minThreshold: z.number().min(0).optional(),
        maxCapacity: z.number().min(0).optional(),
        storageCondition: z.string().optional(),
        location: z.string().optional(),
      }),
    }))
    .mutation(async ({ input, ctx }) => {
      // Implementation
    }),
});

// Stock Transaction Router
export const stockRouter = router({
  // Record stock in
  stockIn: protectedProcedure
    .input(z.object({
      reagentId: z.string().uuid(),
      quantity: z.number().positive(),
      reason: z.string(),
      batchNumber: z.string().optional(),
      expiryDate: z.date().optional(),
      projectCode: z.string().optional(),
      notes: z.string().optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      // Implementation with stock calculation
    }),

  // Record stock out
  stockOut: protectedProcedure
    .input(z.object({
      reagentId: z.string().uuid(),
      quantity: z.number().positive(),
      reason: z.string(),
      projectCode: z.string().optional(),
      notes: z.string().optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      // Implementation with stock validation
    }),

  // Stock adjustment
  adjust: protectedProcedure
    .input(z.object({
      reagentId: z.string().uuid(),
      newQuantity: z.number().min(0),
      reason: z.string(),
      notes: z.string().optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      // Implementation
    }),

  // Get transaction history
  getHistory: publicProcedure
    .input(z.object({
      reagentId: z.string().uuid().optional(),
      userId: z.string().uuid().optional(),
      type: z.nativeEnum(TransactionType).optional(),
      startDate: z.date().optional(),
      endDate: z.date().optional(),
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ input, ctx }) => {
      // Implementation
    }),
});

// User Router
export const userRouter = router({
  // Get current user profile
  me: protectedProcedure
    .query(async ({ ctx }) => {
      // Implementation
    }),

  // Update user preferences
  updatePreferences: protectedProcedure
    .input(z.object({
      theme: z.enum(['light', 'dark']).optional(),
      language: z.enum(['zh-CN', 'en-US']).optional(),
      defaultView: z.enum(['list', 'grid']).optional(),
      itemsPerPage: z.number().min(10).max(100).optional(),
      favoriteReagents: z.array(z.string().uuid()).optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      // Implementation
    }),

  // Get all users (admin only)
  getAll: protectedProcedure
    .input(z.object({
      role: z.nativeEnum(UserRole).optional(),
      department: z.string().optional(),
      isActive: z.boolean().optional(),
    }))
    .query(async ({ input, ctx }) => {
      // Implementation with role check
    }),
});

// Alert Router
export const alertRouter = router({
  // Get user alerts
  getMyAlerts: protectedProcedure
    .input(z.object({
      unreadOnly: z.boolean().default(false),
      type: z.nativeEnum(AlertType).optional(),
      limit: z.number().min(1).max(50).default(20),
    }))
    .query(async ({ input, ctx }) => {
      // Implementation
    }),

  // Mark alert as read
  markRead: protectedProcedure
    .input(z.string().uuid())
    .mutation(async ({ input, ctx }) => {
      // Implementation
    }),

  // Resolve alert
  resolve: protectedProcedure
    .input(z.string().uuid())
    .mutation(async ({ input, ctx }) => {
      // Implementation
    }),
});

// Analytics Router
export const analyticsRouter = router({
  // Dashboard statistics
  getDashboardStats: publicProcedure
    .query(async ({ ctx }) => {
      // Implementation returning overview metrics
    }),

  // Usage analytics
  getUsageAnalytics: publicProcedure
    .input(z.object({
      startDate: z.date(),
      endDate: z.date(),
      groupBy: z.enum(['day', 'week', 'month']).default('day'),
    }))
    .query(async ({ ctx, input }) => {
      // Implementation
    }),

  // Stock level trends
  getStockTrends: publicProcedure
    .input(z.object({
      reagentIds: z.array(z.string().uuid()).optional(),
      period: z.enum(['7d', '30d', '90d']).default('30d'),
    }))
    .query(async ({ ctx, input }) => {
      // Implementation
    }),
});

// Main app router
export const appRouter = router({
  reagent: reagentRouter,
  stock: stockRouter,
  user: userRouter,
  alert: alertRouter,
  analytics: analyticsRouter,
});

export type AppRouter = typeof appRouter;
```
