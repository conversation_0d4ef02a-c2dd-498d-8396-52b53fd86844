# 耗材库管理web应用 Fullstack Architecture Document

## Table of Contents

- [耗材库管理web应用 Fullstack Architecture Document](#table-of-contents)
  - [Introduction](./introduction.md)
    - [Starter Template or Existing Project](./introduction.md#starter-template-or-existing-project)
    - [Change Log](./introduction.md#change-log)
  - [High Level Architecture](./high-level-architecture.md)
    - [Technical Summary](./high-level-architecture.md#technical-summary)
    - [Platform and Infrastructure Choice](./high-level-architecture.md#platform-and-infrastructure-choice)
    - [Repository Structure](./high-level-architecture.md#repository-structure)
    - [High Level Architecture Diagram](./high-level-architecture.md#high-level-architecture-diagram)
    - [Architectural Patterns](./high-level-architecture.md#architectural-patterns)
  - [Tech Stack](./tech-stack.md)
    - [Technology Stack Table](./tech-stack.md#technology-stack-table)
  - [Data Models](./data-models.md)
    - [Reagent](./data-models.md#reagent)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [StockTransaction](./data-models.md#stocktransaction)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [User](./data-models.md#user)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
    - [StockAlert](./data-models.md#stockalert)
      - [TypeScript Interface](./data-models.md#typescript-interface)
      - [Relationships](./data-models.md#relationships)
  - [API Specification](./api-specification.md)
    - [tRPC Router Definitions](./api-specification.md#trpc-router-definitions)
  - [Components](./components.md)
    - [Frontend Application (Next.js)](./components.md#frontend-application-nextjs)
    - [Backend API Server (Express + tRPC)](./components.md#backend-api-server-express-trpc)
    - [Search Engine (Elasticsearch)](./components.md#search-engine-elasticsearch)
    - [Database Layer (PostgreSQL)](./components.md#database-layer-postgresql)
    - [Cache Layer (Redis)](./components.md#cache-layer-redis)
    - [File Storage Service](./components.md#file-storage-service)
    - [Component Diagrams](./components.md#component-diagrams)
  - [External APIs](./external-apis.md)
  - [Core Workflows](./core-workflows.md)
    - [Reagent Search and View Workflow](./core-workflows.md#reagent-search-and-view-workflow)
    - [Stock Transaction Workflow](./core-workflows.md#stock-transaction-workflow)
    - [Data Import Workflow](./core-workflows.md#data-import-workflow)
  - [Database Schema](./database-schema.md)
  - [Frontend Architecture](./frontend-architecture.md)
    - [Component Architecture](./frontend-architecture.md#component-architecture)
      - [Component Organization](./frontend-architecture.md#component-organization)
      - [Component Template](./frontend-architecture.md#component-template)
    - [State Management Architecture](./frontend-architecture.md#state-management-architecture)
      - [State Structure](./frontend-architecture.md#state-structure)
      - [State Management Patterns](./frontend-architecture.md#state-management-patterns)
    - [Routing Architecture](./frontend-architecture.md#routing-architecture)
      - [Route Organization](./frontend-architecture.md#route-organization)
      - [Protected Route Pattern](./frontend-architecture.md#protected-route-pattern)
    - [Frontend Services Layer](./frontend-architecture.md#frontend-services-layer)
      - [API Client Setup](./frontend-architecture.md#api-client-setup)
      - [Service Example](./frontend-architecture.md#service-example)
  - [Backend Architecture](./backend-architecture.md)
    - [Service Architecture](./backend-architecture.md#service-architecture)
      - [Controller/Route Organization](./backend-architecture.md#controllerroute-organization)
      - [Controller Template](./backend-architecture.md#controller-template)
    - [Database Architecture](./backend-architecture.md#database-architecture)
      - [Schema Design](./backend-architecture.md#schema-design)
      - [Data Access Layer](./backend-architecture.md#data-access-layer)
    - [Authentication and Authorization](./backend-architecture.md#authentication-and-authorization)
      - [Auth Flow](./backend-architecture.md#auth-flow)
      - [Middleware/Guards](./backend-architecture.md#middlewareguards)
  - [Unified Project Structure](./unified-project-structure.md)
  - [Development Workflow](./development-workflow.md)
    - [Local Development Setup](./development-workflow.md#local-development-setup)
      - [Prerequisites](./development-workflow.md#prerequisites)
      - [Initial Setup](./development-workflow.md#initial-setup)
      - [Development Commands](./development-workflow.md#development-commands)
    - [Environment Configuration](./development-workflow.md#environment-configuration)
      - [Required Environment Variables](./development-workflow.md#required-environment-variables)
  - [Deployment Architecture](./deployment-architecture.md)
    - [Deployment Strategy](./deployment-architecture.md#deployment-strategy)
    - [CI/CD Pipeline](./deployment-architecture.md#cicd-pipeline)
    - [Environments](./deployment-architecture.md#environments)
