# Frontend Architecture

## Component Architecture

### Component Organization

```
src/
├── components/           # Reusable UI components
│   ├── ui/              # Basic UI components (Button, Input, etc.)
│   ├── forms/           # Form components (ReagentForm, StockForm)
│   ├── tables/          # Data table components
│   ├── charts/          # Data visualization components
│   └── layout/          # Layout components (Header, Sidebar, etc.)
├── pages/               # Next.js pages/routes
│   ├── dashboard/       # Dashboard page
│   ├── reagents/        # Reagent management pages
│   ├── stock/           # Stock operation pages
│   ├── analytics/       # Analytics and reports
│   └── admin/           # Admin pages
├── hooks/               # Custom React hooks
├── stores/              # Zustand state stores
├── services/            # API client services
├── utils/               # Utility functions
└── types/               # TypeScript type definitions
```

### Component Template

```typescript
import React from 'react';
import { Card, Button, Space } from 'antd';
import { useReagentStore } from '@/stores/reagentStore';
import { trpc } from '@/utils/trpc';

interface ReagentCardProps {
  reagent: Reagent;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
}

export const ReagentCard: React.FC<ReagentCardProps> = ({
  reagent,
  onEdit,
  onDelete,
}) => {
  const { updateReagent } = useReagentStore();
  const utils = trpc.useContext();

  const updateMutation = trpc.reagent.update.useMutation({
    onSuccess: (data) => {
      updateReagent(data);
      utils.reagent.search.invalidate();
    },
  });

  const handleQuickEdit = () => {
    // Component logic
  };

  return (
    <Card
      title={reagent.name}
      extra={
        <Space>
          <Button onClick={() => onEdit?.(reagent.id)}>Edit</Button>
          <Button danger onClick={() => onDelete?.(reagent.id)}>
            Delete
          </Button>
        </Space>
      }
    >
      <p>Code: {reagent.code}</p>
      <p>Stock: {reagent.currentStock} {reagent.unit}</p>
      <p>Supplier: {reagent.supplier}</p>
    </Card>
  );
};
```

## State Management Architecture

### State Structure

```typescript
// stores/reagentStore.ts
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface ReagentState {
  reagents: Reagent[];
  selectedReagent: Reagent | null;
  searchQuery: string;
  filters: ReagentFilters;
  loading: boolean;
  error: string | null;
}

interface ReagentActions {
  setReagents: (reagents: Reagent[]) => void;
  addReagent: (reagent: Reagent) => void;
  updateReagent: (reagent: Reagent) => void;
  removeReagent: (id: string) => void;
  setSelectedReagent: (reagent: Reagent | null) => void;
  setSearchQuery: (query: string) => void;
  setFilters: (filters: Partial<ReagentFilters>) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
}

type ReagentStore = ReagentState & ReagentActions;

export const useReagentStore = create<ReagentStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      reagents: [],
      selectedReagent: null,
      searchQuery: '',
      filters: {},
      loading: false,
      error: null,

      // Actions
      setReagents: (reagents) => set({ reagents }),
      addReagent: (reagent) =>
        set((state) => ({ reagents: [...state.reagents, reagent] })),
      updateReagent: (reagent) =>
        set((state) => ({
          reagents: state.reagents.map((r) =>
            r.id === reagent.id ? reagent : r
          ),
        })),
      removeReagent: (id) =>
        set((state) => ({
          reagents: state.reagents.filter((r) => r.id !== id),
        })),
      setSelectedReagent: (reagent) => set({ selectedReagent: reagent }),
      setSearchQuery: (query) => set({ searchQuery: query }),
      setFilters: (filters) =>
        set((state) => ({ filters: { ...state.filters, ...filters } })),
      setLoading: (loading) => set({ loading }),
      setError: (error) => set({ error }),
      reset: () =>
        set({
          reagents: [],
          selectedReagent: null,
          searchQuery: '',
          filters: {},
          loading: false,
          error: null,
        }),
    }),
    { name: 'reagent-store' }
  )
);
```

### State Management Patterns

- **Single Source of Truth:** Each domain (reagents, stock, users) has its own Zustand store
- **Optimistic Updates:** UI updates immediately, with rollback on API failure
- **Cache Synchronization:** tRPC handles server state, Zustand manages client state
- **Persistence:** Critical user preferences stored in localStorage
- **Real-time Updates:** WebSocket integration updates stores automatically

## Routing Architecture

### Route Organization

```
pages/
├── index.tsx                    # Dashboard (/)
├── login.tsx                    # Login page (/login)
├── reagents/
│   ├── index.tsx               # Reagent list (/reagents)
│   ├── [id].tsx                # Reagent detail (/reagents/[id])
│   ├── new.tsx                 # Create reagent (/reagents/new)
│   └── edit/[id].tsx           # Edit reagent (/reagents/edit/[id])
├── stock/
│   ├── index.tsx               # Stock operations (/stock)
│   ├── in.tsx                  # Stock in (/stock/in)
│   ├── out.tsx                 # Stock out (/stock/out)
│   └── adjust.tsx              # Stock adjustment (/stock/adjust)
├── analytics/
│   ├── index.tsx               # Analytics dashboard (/analytics)
│   ├── usage.tsx               # Usage reports (/analytics/usage)
│   └── trends.tsx              # Trend analysis (/analytics/trends)
├── admin/
│   ├── index.tsx               # Admin dashboard (/admin)
│   ├── users.tsx               # User management (/admin/users)
│   └── settings.tsx            # System settings (/admin/settings)
└── api/
    ├── trpc/[trpc].ts          # tRPC API routes
    └── auth/[...nextauth].ts   # NextAuth.js routes
```

### Protected Route Pattern

```typescript
// components/auth/ProtectedRoute.tsx
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { Spin } from 'antd';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: UserRole;
  fallback?: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  fallback,
}) => {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (!session) {
      router.push('/login');
      return;
    }

    if (requiredRole && !hasRole(session.user.role, requiredRole)) {
      router.push('/unauthorized');
      return;
    }
  }, [session, status, router, requiredRole]);

  if (status === 'loading') {
    return fallback || <Spin size="large" />;
  }

  if (!session) {
    return null;
  }

  if (requiredRole && !hasRole(session.user.role, requiredRole)) {
    return null;
  }

  return <>{children}</>;
};

// Helper function to check role hierarchy
function hasRole(userRole: UserRole, requiredRole: UserRole): boolean {
  const roleHierarchy = {
    VIEWER: 1,
    OPERATOR: 2,
    MANAGER: 3,
    ADMIN: 4,
  };

  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
}
```

## Frontend Services Layer

### API Client Setup

```typescript
// utils/trpc.ts
import { createTRPCNext } from '@trpc/next';
import { httpBatchLink } from '@trpc/client';
import type { AppRouter } from '../../../apps/api/src/router';

function getBaseUrl() {
  if (typeof window !== 'undefined') return ''; // browser should use relative url
  if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`; // SSR should use vercel url
  return `http://localhost:${process.env.PORT ?? 3000}`; // dev SSR should use localhost
}

export const trpc = createTRPCNext<AppRouter>({
  config() {
    return {
      links: [
        httpBatchLink({
          url: `${getBaseUrl()}/api/trpc`,
          headers() {
            return {
              // Add auth headers if needed
            };
          },
        }),
      ],
      queryClientConfig: {
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000, // 1 minute
            retry: 1,
          },
        },
      },
    };
  },
  ssr: false,
});
```

### Service Example

```typescript
// services/reagentService.ts
import { trpc } from '@/utils/trpc';
import { useReagentStore } from '@/stores/reagentStore';

export const useReagentService = () => {
  const { setReagents, setLoading, setError } = useReagentStore();

  const searchReagents = trpc.reagent.search.useQuery(
    {
      query: '',
      limit: 20,
      offset: 0,
    },
    {
      onSuccess: (data) => {
        setReagents(data.reagents);
        setLoading(false);
      },
      onError: (error) => {
        setError(error.message);
        setLoading(false);
      },
    }
  );

  const createReagent = trpc.reagent.create.useMutation({
    onSuccess: (newReagent) => {
      // Optimistic update
      useReagentStore.getState().addReagent(newReagent);
      // Invalidate and refetch
      trpc.useContext().reagent.search.invalidate();
    },
    onError: (error) => {
      setError(error.message);
    },
  });

  const updateReagent = trpc.reagent.update.useMutation({
    onMutate: async (variables) => {
      // Optimistic update
      const previousReagents = useReagentStore.getState().reagents;
      useReagentStore.getState().updateReagent({
        ...previousReagents.find(r => r.id === variables.id)!,
        ...variables.data,
      });
      return { previousReagents };
    },
    onError: (error, variables, context) => {
      // Rollback on error
      if (context?.previousReagents) {
        setReagents(context.previousReagents);
      }
      setError(error.message);
    },
    onSettled: () => {
      trpc.useContext().reagent.search.invalidate();
    },
  });

  return {
    searchReagents,
    createReagent,
    updateReagent,
    isLoading: searchReagents.isLoading,
    error: searchReagents.error,
  };
};
```
