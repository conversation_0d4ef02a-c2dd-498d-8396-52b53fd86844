# Backend Architecture

## Service Architecture

### Controller/Route Organization

```
src/
├── routes/              # tRPC routers
│   ├── reagent.ts      # Reagent operations
│   ├── stock.ts        # Stock transactions
│   ├── user.ts         # User management
│   ├── alert.ts        # Alert system
│   └── analytics.ts    # Analytics and reports
├── services/           # Business logic services
│   ├── ReagentService.ts
│   ├── StockService.ts
│   ├── UserService.ts
│   ├── AlertService.ts
│   └── SearchService.ts
├── models/             # Data models and repositories
│   ├── ReagentRepository.ts
│   ├── StockRepository.ts
│   └── UserRepository.ts
├── middleware/         # Express middleware
│   ├── auth.ts         # Authentication
│   ├── validation.ts   # Request validation
│   ├── logging.ts      # Request logging
│   └── error.ts        # Error handling
├── utils/              # Utility functions
│   ├── database.ts     # Database connection
│   ├── elasticsearch.ts # ES client
│   ├── redis.ts        # Redis client
│   └── logger.ts       # Winston logger
└── server.ts           # Express server setup
```

### Controller Template

```typescript
// routes/reagent.ts
import { z } from 'zod';
import { router, publicProcedure, protectedProcedure } from '../trpc';
import { ReagentService } from '../services/ReagentService';
import { TRPCError } from '@trpc/server';

const reagentService = new ReagentService();

export const reagentRouter = router({
  search: publicProcedure
    .input(z.object({
      query: z.string().optional(),
      category: z.nativeEnum(ReagentCategory).optional(),
      supplier: z.string().optional(),
      lowStock: z.boolean().optional(),
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ input, ctx }) => {
      try {
        const results = await reagentService.search(input);
        return results;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to search reagents',
          cause: error,
        });
      }
    }),

  create: protectedProcedure
    .input(z.object({
      code: z.string().min(1),
      name: z.string().min(1),
      specification: z.string(),
      supplier: z.string(),
      category: z.nativeEnum(ReagentCategory),
      unit: z.string(),
      minThreshold: z.number().min(0),
      maxCapacity: z.number().min(0),
      storageCondition: z.string(),
      location: z.string(),
    }))
    .mutation(async ({ input, ctx }) => {
      if (!ctx.user || !['ADMIN', 'MANAGER'].includes(ctx.user.role)) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Insufficient permissions',
        });
      }

      try {
        const reagent = await reagentService.create(input, ctx.user.id);
        return reagent;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create reagent',
          cause: error,
        });
      }
    }),
});
```

## Database Architecture

### Schema Design

```sql
-- Core schema already defined above in Database Schema section
-- Additional views and functions for performance

-- View for reagent search with computed fields
CREATE VIEW reagent_search_view AS
SELECT
    r.*,
    CASE
        WHEN r.current_stock <= 0 THEN 'OUT_OF_STOCK'
        WHEN r.current_stock <= r.min_threshold THEN 'LOW_STOCK'
        ELSE 'IN_STOCK'
    END as stock_status,
    (SELECT COUNT(*) FROM stock_transactions st WHERE st.reagent_id = r.id) as transaction_count,
    (SELECT MAX(st.created_at) FROM stock_transactions st WHERE st.reagent_id = r.id) as last_transaction_date
FROM reagents r
WHERE r.is_active = true;

-- Function for stock level calculation
CREATE OR REPLACE FUNCTION calculate_stock_level(reagent_id UUID)
RETURNS DECIMAL(10,3) AS $$
DECLARE
    total_stock DECIMAL(10,3) := 0;
BEGIN
    SELECT COALESCE(SUM(
        CASE
            WHEN type IN ('STOCK_IN', 'ADJUSTMENT') THEN quantity
            WHEN type = 'STOCK_OUT' THEN -quantity
            ELSE 0
        END
    ), 0) INTO total_stock
    FROM stock_transactions
    WHERE reagent_id = $1;

    RETURN total_stock;
END;
$$ LANGUAGE plpgsql;
```

### Data Access Layer

```typescript
// models/ReagentRepository.ts
import { PrismaClient } from '@prisma/client';
import { Reagent, ReagentCategory } from '@/types';

export class ReagentRepository {
  constructor(private prisma: PrismaClient) {}

  async findMany(filters: {
    query?: string;
    category?: ReagentCategory;
    supplier?: string;
    lowStock?: boolean;
    limit: number;
    offset: number;
  }): Promise<{ reagents: Reagent[]; total: number }> {
    const where: any = {
      isActive: true,
    };

    if (filters.query) {
      where.OR = [
        { name: { contains: filters.query, mode: 'insensitive' } },
        { code: { contains: filters.query, mode: 'insensitive' } },
        { specification: { contains: filters.query, mode: 'insensitive' } },
      ];
    }

    if (filters.category) {
      where.category = filters.category;
    }

    if (filters.supplier) {
      where.supplier = { contains: filters.supplier, mode: 'insensitive' };
    }

    if (filters.lowStock) {
      where.currentStock = { lte: { field: 'minThreshold' } };
    }

    const [reagents, total] = await Promise.all([
      this.prisma.reagent.findMany({
        where,
        skip: filters.offset,
        take: filters.limit,
        orderBy: { name: 'asc' },
        include: {
          _count: {
            select: { stockTransactions: true },
          },
        },
      }),
      this.prisma.reagent.count({ where }),
    ]);

    return { reagents, total };
  }

  async findById(id: string): Promise<Reagent | null> {
    return this.prisma.reagent.findUnique({
      where: { id },
      include: {
        stockTransactions: {
          orderBy: { createdAt: 'desc' },
          take: 10,
          include: { user: { select: { name: true } } },
        },
        createdBy: { select: { name: true } },
        updatedBy: { select: { name: true } },
      },
    });
  }

  async create(data: Omit<Reagent, 'id' | 'createdAt' | 'updatedAt'>): Promise<Reagent> {
    return this.prisma.reagent.create({
      data,
    });
  }

  async update(id: string, data: Partial<Reagent>): Promise<Reagent> {
    return this.prisma.reagent.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date(),
      },
    });
  }

  async updateStock(id: string, newStock: number): Promise<void> {
    await this.prisma.reagent.update({
      where: { id },
      data: { currentStock: newStock },
    });
  }
}
```

## Authentication and Authorization

### Auth Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant NextAuth
    participant API
    participant Database

    User->>Frontend: Login request
    Frontend->>NextAuth: Authenticate
    NextAuth->>Database: Verify credentials
    Database-->>NextAuth: User data
    NextAuth-->>Frontend: JWT token + session
    Frontend-->>User: Login success

    User->>Frontend: API request
    Frontend->>API: Request with JWT
    API->>NextAuth: Verify token
    NextAuth-->>API: User session
    API->>API: Check permissions
    API-->>Frontend: Response
    Frontend-->>User: Display result
```

### Middleware/Guards

```typescript
// middleware/auth.ts
import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

export async function authMiddleware(req: NextRequest) {
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

  if (!token) {
    return NextResponse.redirect(new URL('/login', req.url));
  }

  // Add user info to headers for API routes
  const requestHeaders = new Headers(req.headers);
  requestHeaders.set('x-user-id', token.sub!);
  requestHeaders.set('x-user-role', token.role as string);

  return NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });
}

// tRPC context with auth
export const createTRPCContext = async ({ req, res }: CreateNextContextOptions) => {
  const session = await getServerSession(req, res, authOptions);

  return {
    req,
    res,
    session,
    user: session?.user,
    prisma,
    elasticsearch,
    redis,
  };
};
```
