# High Level Architecture

## Technical Summary

The 耗材库管理web应用 employs a modern monolithic fullstack architecture deployed via Docker containers. The frontend uses Next.js with React and Ant Design for responsive UI, while the backend leverages Node.js with Express and tRPC for type-safe APIs. PostgreSQL serves as the primary database with Elasticsearch providing high-performance search capabilities for 18,000+ reagent records. The system integrates Redis for caching and session management, supports PWA functionality for mobile lab use, and implements real-time collaboration features for the 20-person research team. This architecture prioritizes search performance (<1s response), mobile optimization (40% usage target), and data accuracy (>99%) while maintaining development simplicity for the small team.

## Platform and Infrastructure Choice

**Platform:** Docker + Traditional Server Deployment
**Key Services:** 
- Application: Docker containers on Linux servers
- Database: PostgreSQL 15 + Elasticsearch 8
- Cache: Redis 7
- File Storage: Local filesystem with backup
- Monitoring: Prometheus + Grafana

**Deployment Host and Regions:** Internal network deployment within research facility

**Rationale:** Traditional server deployment chosen over cloud for:
- Data sovereignty requirements in research environment
- Predictable performance with large datasets
- Cost control for internal use
- Network security within facility

## Repository Structure

**Structure:** Monorepo with TypeScript
**Monorepo Tool:** Turborepo
**Package Organization:** 
- apps/web (Next.js frontend)
- apps/api (Express backend)
- packages/shared (types, utilities)
- packages/ui (shared components)

## High Level Architecture Diagram

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Browser]
        MOBILE[Mobile Browser]
        PWA[PWA App]
    end
    
    subgraph "CDN/Edge"
        NGINX[Nginx Reverse Proxy]
    end
    
    subgraph "Application Layer"
        NEXT[Next.js Frontend]
        API[Express API Server]
    end
    
    subgraph "Data Layer"
        PG[(PostgreSQL)]
        ES[(Elasticsearch)]
        REDIS[(Redis Cache)]
    end
    
    subgraph "Storage"
        FILES[File Storage]
        BACKUP[Backup Storage]
    end
    
    WEB --> NGINX
    MOBILE --> NGINX
    PWA --> NGINX
    
    NGINX --> NEXT
    NGINX --> API
    
    NEXT --> API
    API --> PG
    API --> ES
    API --> REDIS
    API --> FILES
    
    PG --> BACKUP
    FILES --> BACKUP
```

## Architectural Patterns

- **Monolithic Architecture:** Single deployable unit for simplified operations - _Rationale:_ Team size and complexity management, easier debugging and deployment
- **Component-Based UI:** Reusable React components with TypeScript - _Rationale:_ Maintainability and consistency across large interface
- **Repository Pattern:** Abstract data access logic - _Rationale:_ Enables testing and future database migration flexibility  
- **API Gateway Pattern:** tRPC router as single API entry point - _Rationale:_ Type safety and centralized request handling
- **CQRS Pattern:** Separate read/write operations for search - _Rationale:_ Optimize search performance with Elasticsearch while maintaining data integrity in PostgreSQL
- **PWA Pattern:** Progressive Web App for mobile experience - _Rationale:_ Native-like experience for lab environment usage
