# 编码规范 (Coding Standards)

## 概述

本文档定义了试剂库存管理系统的编码规范，确保代码质量、一致性和可维护性。所有团队成员必须遵循这些规范。

## 技术栈规范

### TypeScript 规范

#### 基础配置
- **版本**: TypeScript 5.3+
- **严格模式**: 启用所有严格类型检查
- **目标**: ES2022
- **模块系统**: ESNext (前端) / CommonJS (后端)

#### 类型定义规范
```typescript
// ✅ 正确：使用接口定义对象类型
interface ReagentData {
  id: string;
  name: string;
  code: string;
  category: ReagentCategory;
  supplier: string;
  specification: string;
  currentStock: number;
  availableStock: number;
  unit: string;
  storageCondition: string;
  expiryDate: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

// ✅ 正确：使用枚举定义常量
enum ReagentCategory {
  BIOLOGICAL_REAGENT = 'YF03',
  LABORATORY_CONSUMABLE = 'YF04',
  CULTURE_MEDIUM = 'YF06',
}

// ✅ 正确：使用泛型提高复用性
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
}

// ❌ 错误：使用 any 类型
const data: any = fetchData();

// ✅ 正确：使用具体类型或泛型
const data: ReagentData[] = fetchReagents();
```

#### 命名规范
```typescript
// 接口和类型：PascalCase
interface UserPreferences { }
type SearchFilters = { };

// 变量和函数：camelCase
const currentUser = getCurrentUser();
const searchReagents = (filters: SearchFilters) => { };

// 常量：SCREAMING_SNAKE_CASE
const MAX_SEARCH_RESULTS = 100;
const API_ENDPOINTS = {
  REAGENTS: '/api/reagents',
  INVENTORY: '/api/inventory',
} as const;

// 组件：PascalCase
const ReagentSearchForm = () => { };
const InventoryDashboard = () => { };
```

### React/Next.js 规范

#### 组件结构
```typescript
// ✅ 正确：函数组件结构
interface ReagentCardProps {
  reagent: ReagentData;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  className?: string;
}

export const ReagentCard: React.FC<ReagentCardProps> = ({
  reagent,
  onEdit,
  onDelete,
  className,
}) => {
  // Hooks 在顶部
  const [isLoading, setIsLoading] = useState(false);
  const { mutate: updateReagent } = useUpdateReagent();

  // 事件处理函数
  const handleEdit = useCallback(() => {
    onEdit?.(reagent.id);
  }, [onEdit, reagent.id]);

  // 渲染逻辑
  return (
    <Card className={cn('reagent-card', className)}>
      {/* 组件内容 */}
    </Card>
  );
};
```

#### Hooks 使用规范
```typescript
// ✅ 正确：自定义 Hook
export const useReagentSearch = (initialFilters?: SearchFilters) => {
  const [filters, setFilters] = useState(initialFilters || {});
  const [isLoading, setIsLoading] = useState(false);

  const { data, error, mutate } = useSWR(
    ['reagents', filters],
    () => searchReagents(filters),
    {
      revalidateOnFocus: false,
      dedupingInterval: 5000,
    }
  );

  return {
    reagents: data?.data || [],
    isLoading: isLoading || !data,
    error,
    filters,
    setFilters,
    refetch: mutate,
  };
};

// ✅ 正确：使用 useCallback 优化性能
const handleSearch = useCallback(
  debounce((query: string) => {
    setFilters(prev => ({ ...prev, query }));
  }, 300),
  [setFilters]
);
```

### API 设计规范 (tRPC)

#### 路由定义
```typescript
// ✅ 正确：tRPC 路由结构
export const reagentRouter = router({
  // 查询操作：使用 query
  search: publicProcedure
    .input(z.object({
      query: z.string().optional(),
      category: z.nativeEnum(ReagentCategory).optional(),
      supplier: z.string().optional(),
      lowStock: z.boolean().optional(),
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ input, ctx }) => {
      // 实现搜索逻辑
      return await ctx.db.reagent.findMany({
        where: buildSearchFilters(input),
        take: input.limit,
        skip: input.offset,
        orderBy: { name: 'asc' },
      });
    }),

  // 修改操作：使用 mutation
  create: protectedProcedure
    .input(CreateReagentSchema)
    .mutation(async ({ input, ctx }) => {
      // 验证权限
      await validateUserPermission(ctx.user, 'reagent:create');
      
      // 创建试剂
      return await ctx.db.reagent.create({
        data: {
          ...input,
          createdBy: ctx.user.id,
        },
      });
    }),
});
```

#### 数据验证 (Zod)
```typescript
// ✅ 正确：Zod Schema 定义
export const CreateReagentSchema = z.object({
  name: z.string().min(1, '试剂名称不能为空').max(200),
  code: z.string().regex(/^YF\d{2}\.\d{3}$/, '编码格式不正确'),
  category: z.nativeEnum(ReagentCategory),
  supplier: z.string().min(1, '供应商不能为空'),
  specification: z.string().min(1, '规格不能为空'),
  unit: z.string().min(1, '单位不能为空'),
  storageCondition: z.string().optional(),
  initialStock: z.number().min(0, '初始库存不能为负数'),
});

export const UpdateInventorySchema = z.object({
  reagentId: z.string().uuid(),
  operation: z.enum(['IN', 'OUT', 'ADJUST']),
  quantity: z.number(),
  reason: z.string().min(1, '操作原因不能为空'),
  batchNumber: z.string().optional(),
  expiryDate: z.date().optional(),
});
```

### 数据库规范 (Prisma)

#### Schema 设计
```prisma
// ✅ 正确：Prisma Schema 结构
model Reagent {
  id                String            @id @default(cuid())
  name              String            @db.VarChar(200)
  code              String            @unique @db.VarChar(20)
  category          ReagentCategory
  supplier          String            @db.VarChar(100)
  specification     String            @db.VarChar(200)
  unit              String            @db.VarChar(20)
  storageCondition  String?           @db.VarChar(100)
  currentStock      Decimal           @default(0) @db.Decimal(10, 2)
  availableStock    Decimal           @default(0) @db.Decimal(10, 2)
  minStockLevel     Decimal?          @db.Decimal(10, 2)
  maxStockLevel     Decimal?          @db.Decimal(10, 2)
  isActive          Boolean           @default(true)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  createdBy         String
  
  // 关联关系
  creator           User              @relation("ReagentCreator", fields: [createdBy], references: [id])
  inventoryLogs     InventoryLog[]
  stockAlerts       StockAlert[]
  
  // 索引优化
  @@index([category, isActive])
  @@index([name])
  @@index([code])
  @@index([supplier])
  @@map("reagents")
}

enum ReagentCategory {
  YF03  // 生物试剂
  YF04  // 实验耗材
  YF06  // 培养基
  
  @@map("reagent_categories")
}
```

## 代码质量规范

### ESLint 配置
```json
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended",
    "@typescript-eslint/recommended-requiring-type-checking"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/prefer-const": "error",
    "@typescript-eslint/no-non-null-assertion": "warn",
    "react-hooks/exhaustive-deps": "error",
    "import/order": ["error", {
      "groups": ["builtin", "external", "internal", "parent", "sibling"],
      "newlines-between": "always"
    }]
  }
}
```

### Prettier 配置
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "arrowParens": "avoid"
}
```

## 测试规范

### 单元测试 (Jest/Vitest)
```typescript
// ✅ 正确：测试文件结构
describe('ReagentService', () => {
  describe('searchReagents', () => {
    it('should return reagents matching search query', async () => {
      // Arrange
      const mockReagents = [
        createMockReagent({ name: '亘诺细胞冻存液' }),
        createMockReagent({ name: '科源S5培养基' }),
      ];
      jest.spyOn(db.reagent, 'findMany').mockResolvedValue(mockReagents);

      // Act
      const result = await searchReagents({ query: '亘诺' });

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].name).toContain('亘诺');
    });

    it('should handle empty search results', async () => {
      // Arrange
      jest.spyOn(db.reagent, 'findMany').mockResolvedValue([]);

      // Act
      const result = await searchReagents({ query: 'nonexistent' });

      // Assert
      expect(result).toEqual([]);
    });
  });
});
```

### 集成测试
```typescript
// ✅ 正确：API 集成测试
describe('/api/reagents', () => {
  beforeEach(async () => {
    await cleanDatabase();
    await seedTestData();
  });

  it('should create new reagent with valid data', async () => {
    const reagentData = {
      name: '测试试剂',
      code: 'YF03.001',
      category: 'YF03',
      supplier: '测试供应商',
      specification: '100ml',
      unit: '瓶',
      initialStock: 10,
    };

    const response = await request(app)
      .post('/api/reagents')
      .send(reagentData)
      .expect(201);

    expect(response.body.data).toMatchObject({
      name: reagentData.name,
      code: reagentData.code,
    });
  });
});
```

## Git 工作流规范

### 分支命名
```bash
# 功能分支
feature/reagent-search-optimization
feature/mobile-responsive-design

# 修复分支
fix/inventory-calculation-bug
fix/search-performance-issue

# 发布分支
release/v1.2.0

# 热修复分支
hotfix/critical-security-patch
```

### 提交信息规范
```bash
# 格式：<type>(<scope>): <description>

# 功能
feat(search): add advanced filtering for reagent search
feat(inventory): implement batch stock update

# 修复
fix(api): resolve inventory calculation error
fix(ui): correct mobile layout issues

# 文档
docs(readme): update installation instructions
docs(api): add tRPC endpoint documentation

# 重构
refactor(components): extract common search logic
refactor(database): optimize reagent query performance

# 测试
test(reagent): add unit tests for search service
test(e2e): add inventory management flow tests
```

## 性能优化规范

### 前端性能
```typescript
// ✅ 正确：使用 React.memo 优化渲染
export const ReagentCard = React.memo<ReagentCardProps>(({ reagent }) => {
  return <Card>{/* 组件内容 */}</Card>;
});

// ✅ 正确：使用 useMemo 缓存计算结果
const filteredReagents = useMemo(() => {
  return reagents.filter(reagent => 
    reagent.name.toLowerCase().includes(searchQuery.toLowerCase())
  );
}, [reagents, searchQuery]);

// ✅ 正确：懒加载组件
const InventoryChart = lazy(() => import('./InventoryChart'));
```

### 后端性能
```typescript
// ✅ 正确：数据库查询优化
const searchReagents = async (filters: SearchFilters) => {
  return await db.reagent.findMany({
    where: {
      AND: [
        filters.query ? {
          OR: [
            { name: { contains: filters.query, mode: 'insensitive' } },
            { code: { contains: filters.query, mode: 'insensitive' } },
            { supplier: { contains: filters.query, mode: 'insensitive' } },
          ],
        } : {},
        filters.category ? { category: filters.category } : {},
        filters.lowStock ? { currentStock: { lt: db.reagent.fields.minStockLevel } } : {},
      ],
    },
    select: {
      id: true,
      name: true,
      code: true,
      category: true,
      currentStock: true,
      unit: true,
      // 只选择需要的字段
    },
    take: filters.limit,
    skip: filters.offset,
  });
};
```

## 安全规范

### 输入验证
```typescript
// ✅ 正确：严格的输入验证
export const validateReagentCode = (code: string): boolean => {
  const codePattern = /^YF(03|04|06)\.\d{3}$/;
  return codePattern.test(code);
};

// ✅ 正确：SQL 注入防护（使用 Prisma）
const searchByCode = async (code: string) => {
  // Prisma 自动防护 SQL 注入
  return await db.reagent.findUnique({
    where: { code }, // 安全的参数化查询
  });
};
```

### 权限控制
```typescript
// ✅ 正确：基于角色的权限验证
export const requirePermission = (permission: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    const user = req.user;
    if (!user || !user.permissions.includes(permission)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    next();
  };
};
```

## 文档规范

### 代码注释
```typescript
/**
 * 搜索试剂库存
 * @param filters - 搜索过滤条件
 * @param filters.query - 搜索关键词（支持名称、编码、供应商）
 * @param filters.category - 试剂分类（YF03/YF04/YF06）
 * @param filters.lowStock - 是否只显示低库存试剂
 * @returns Promise<ReagentData[]> 匹配的试剂列表
 * @throws {ValidationError} 当输入参数无效时
 * @example
 * ```typescript
 * const reagents = await searchReagents({
 *   query: '亘诺',
 *   category: 'YF03',
 *   lowStock: true
 * });
 * ```
 */
export const searchReagents = async (filters: SearchFilters): Promise<ReagentData[]> => {
  // 实现逻辑
};
```

### API 文档
```typescript
// ✅ 正确：tRPC 自动生成类型安全的 API 文档
export const reagentRouter = router({
  search: publicProcedure
    .meta({
      description: '搜索试剂库存',
      tags: ['reagent', 'search'],
    })
    .input(SearchReagentsSchema)
    .output(z.array(ReagentSchema))
    .query(async ({ input }) => {
      // 实现逻辑
    }),
});
```

## 部署和运维规范

### Docker 配置
```dockerfile
# ✅ 正确：多阶段构建优化
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runner
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

### 环境变量管理
```bash
# ✅ 正确：环境变量命名规范
DATABASE_URL=postgresql://user:pass@localhost:5432/haocai
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-secret-key
NODE_ENV=production
LOG_LEVEL=info
```

---

**遵循这些编码规范将确保代码质量、团队协作效率和系统的长期可维护性。**
