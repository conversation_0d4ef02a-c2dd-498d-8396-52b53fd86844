# 耗材库管理web应用 UI/UX Specification

## Introduction

This document defines the user experience goals, information architecture, user flows, and visual design specifications for 耗材库管理web应用's user interface. It serves as the foundation for visual design and frontend development, ensuring a cohesive and user-centered experience.

### Overall UX Goals & Principles

#### Target User Personas

**实验室研究员 (Lab Researcher)**
- 角色：日常进行实验的研发人员，需要频繁查询和使用试剂
- 特点：熟悉科研流程，重视效率和准确性，经常在实验室现场操作
- 需求：快速查找试剂、实时库存信息、移动端便捷访问
- 痛点：现有系统查找效率低，移动端支持不足

**实验室管理员 (Lab Manager)**
- 角色：负责库存管理、采购决策和团队协调的管理人员
- 特点：需要全面的数据视图，关注成本控制和库存优化
- 需求：库存概览、使用统计、采购建议、权限管理
- 痛点：缺乏数据分析工具，无法快速生成报告

**系统管理员 (System Administrator)**
- 角色：负责系统维护、用户管理和数据安全的技术人员
- 特点：关注系统稳定性、安全性和可维护性
- 需求：用户权限控制、操作审计、系统监控
- 痛点：缺乏完善的管理工具和监控机制

#### Usability Goals

- **学习易用性**: 新用户能在5分钟内完成基本的试剂查询任务
- **使用效率**: 熟练用户能在30秒内找到所需试剂信息
- **错误预防**: 关键操作（如库存调整）有清晰的验证和确认机制
- **记忆性**: 偶尔使用的用户能够快速重新上手，无需重新学习
- **移动端优化**: 在实验室现场能够便捷地进行查询和基本操作
- **数据准确性**: 库存信息实时更新，准确率>99%

#### Design Principles

1. **科研专业性优先** - 界面设计体现科研环境的专业性和严谨性，避免过于花哨的设计
2. **效率至上** - 优化常用操作流程，减少点击次数，提供快捷方式和批量操作
3. **移动端友好** - 针对实验室现场使用场景，优化移动端交互和信息展示
4. **渐进式信息披露** - 从概览到详情的层次化展示，避免信息过载
5. **一致性体验** - 在不同设备和功能模块间保持一致的交互模式和视觉风格

### Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-07-30 | 1.0 | Initial UI/UX specification creation | UX Expert |

## Information Architecture (IA)

### Site Map / Screen Inventory

```mermaid
graph TD
    A[登录页面] --> B[主仪表板]

    B --> C[试剂搜索]
    B --> D[库存管理]
    B --> E[数据分析]
    B --> F[系统管理]

    C --> C1[搜索结果列表]
    C --> C2[高级筛选]
    C1 --> C3[试剂详情页]
    C3 --> C4[库存历史]
    C3 --> C5[操作记录]

    D --> D1[入库管理]
    D --> D2[出库管理]
    D --> D3[库存调整]
    D --> D4[批量操作]
    D1 --> D5[入库表单]
    D2 --> D6[出库表单]
    D3 --> D7[调整表单]

    E --> E1[库存概览]
    E --> E2[使用统计]
    E --> E3[预警报告]
    E --> E4[数据导出]

    F --> F1[用户管理]
    F --> F2[权限设置]
    F --> F3[操作日志]
    F --> F4[系统设置]

    B --> G[个人中心]
    G --> G1[个人偏好]
    G --> G2[收藏夹]
    G --> G3[最近查看]

    H[移动端精简版] --> H1[快速搜索]
    H --> H2[常用试剂]
    H --> H3[扫码查询]
    H1 --> H4[试剂卡片视图]
```

### Navigation Structure

**主导航 (Primary Navigation):**
采用顶部水平导航栏设计，包含四个核心功能模块：
- 🏠 仪表板 - 系统首页和概览信息
- 🔍 试剂搜索 - 核心的搜索和浏览功能
- 📦 库存管理 - 入库、出库、调整等操作功能
- 📊 数据分析 - 统计报告和数据可视化
- ⚙️ 系统管理 - 用户权限和系统配置（管理员可见）

**次级导航 (Secondary Navigation):**
- 左侧边栏：在每个主模块内提供子功能导航
- 标签页：在复杂功能内使用标签页组织相关操作
- 快捷操作：右上角提供常用操作的快速入口

**面包屑策略 (Breadcrumb Strategy):**
- 层级面包屑：显示用户在信息架构中的位置
- 属性面包屑：在搜索结果中显示当前筛选条件
- 历史面包屑：在试剂详情页显示访问路径

## User Flows

### 核心用户流程：试剂快速查询

**用户目标：** 在30秒内找到所需试剂的库存信息

**入口点：** 主仪表板搜索框、试剂搜索页面、移动端快速搜索

**成功标准：** 用户能够快速定位试剂并获取准确的库存信息

#### 流程图

```mermaid
graph TD
    A[用户进入系统] --> B[主仪表板]
    B --> C{搜索方式选择}

    C -->|直接搜索| D[输入关键词]
    C -->|分类浏览| E[选择分类]
    C -->|扫码查询| F[扫描条码]

    D --> G[实时搜索建议]
    G --> H[选择建议或继续输入]
    H --> I[搜索结果列表]

    E --> J[分类试剂列表]
    J --> I

    F --> K[识别试剂编码]
    K --> L[直接跳转试剂详情]

    I --> M{结果满意?}
    M -->|是| N[点击查看详情]
    M -->|否| O[调整筛选条件]
    O --> I

    N --> L[试剂详情页]
    L --> P[查看库存信息]
    P --> Q[任务完成]
```

#### 边缘情况与错误处理

- **无搜索结果**：提供搜索建议、拼写检查、相似试剂推荐
- **网络连接问题**：显示缓存结果、离线提示、重试机制
- **权限不足**：友好的权限提示、联系管理员引导
- **数据加载缓慢**：骨架屏加载、进度指示、分页优化

**注意事项：** 移动端优化触摸交互，支持语音搜索和条码扫描

### 库存操作流程：试剂出库

**用户目标：** 记录试剂使用情况并更新库存

**入口点：** 试剂详情页、库存管理页面、快速操作菜单

**成功标准：** 准确记录使用信息，实时更新库存数据

#### 流程图

```mermaid
graph TD
    A[选择试剂] --> B[点击出库操作]
    B --> C[出库表单]
    C --> D[填写使用数量]
    D --> E[选择使用用途]
    E --> F[添加备注说明]
    F --> G[库存充足性检查]

    G -->|库存充足| H[确认出库]
    G -->|库存不足| I[库存不足警告]
    I --> J[调整数量或取消]
    J --> D

    H --> K[更新库存数据]
    K --> L[记录操作日志]
    L --> M[显示成功反馈]
    M --> N[返回试剂详情]
```

#### 边缘情况与错误处理

- **库存不足**：显示当前可用库存、建议替代试剂
- **数据验证失败**：高亮错误字段、提供修正建议
- **网络中断**：本地暂存、网络恢复后同步
- **权限过期**：重新认证、保存当前操作状态

**注意事项：** 支持批量出库、常用用途快速选择、操作撤销功能

### 数据分析流程：库存报告生成

**用户目标：** 生成库存使用统计报告

**入口点：** 数据分析页面、仪表板快捷入口

**成功标准：** 快速生成准确的可视化报告

#### 流程图

```mermaid
graph TD
    A[进入数据分析] --> B[选择报告类型]
    B --> C[设置时间范围]
    C --> D[选择筛选条件]
    D --> E[生成报告预览]
    E --> F{报告满意?}

    F -->|是| G[导出或分享]
    F -->|否| H[调整参数]
    H --> D

    G --> I[选择导出格式]
    I --> J[下载报告文件]
    J --> K[任务完成]
```

#### 边缘情况与错误处理

- **数据量过大**：分页显示、采样展示、后台处理
- **导出失败**：重试机制、格式降级、分批导出
- **权限限制**：数据脱敏、部分可见、申请权限引导

**注意事项：** 支持实时数据更新、多种图表类型、移动端适配

## Wireframes & Mockups

### 设计文件位置

**主要设计文件：** 将使用 Figma 进行详细的视觉设计和原型制作

### 关键页面布局

#### 主仪表板

**目的：** 提供系统概览和快速访问入口

**关键元素：**
- 顶部搜索栏（全局搜索功能）
- 库存概览卡片（总数、低库存预警、最近更新）
- 快速操作按钮（入库、出库、查询）
- 最近查看试剂列表
- 数据可视化图表（库存分布、使用趋势）

**交互说明：** 搜索栏支持实时建议，卡片可点击查看详情，图表支持交互式筛选

**设计文件引用：** Figma - Dashboard Layout v1.0

#### 搜索结果页

**目的：** 高效展示搜索结果和筛选选项

**关键元素：**
- 搜索栏和筛选器（左侧边栏或顶部）
- 试剂卡片列表（包含编码、名称、库存状态、快速操作）
- 排序和视图切换选项
- 分页或无限滚动
- 批量操作工具栏

**交互说明：** 卡片支持悬停预览，筛选器实时更新结果，支持键盘导航

**设计文件引用：** Figma - Search Results v1.0

#### 试剂详情页

**目的：** 展示完整的试剂信息和操作选项

**关键元素：**
- 试剂基本信息（编码、名称、规格、供应商）
- 库存状态指示器（数量、状态、位置）
- 操作按钮组（入库、出库、调整、收藏）
- 历史记录标签页（库存变化、操作日志）
- 相关试剂推荐

**交互说明：** 状态实时更新，操作按钮根据权限显示，历史记录支持筛选和搜索

**设计文件引用：** Figma - Item Detail v1.0

#### 移动端界面

**目的：** 为实验室现场使用优化的简化界面

**关键元素：**
- 大号搜索按钮和语音输入
- 扫码查询功能
- 简化的试剂卡片（关键信息突出）
- 底部导航栏（搜索、收藏、历史、设置）
- 快速操作浮动按钮

**交互说明：** 手势友好的大按钮，支持离线查看，简化的操作流程

**设计文件引用：** Figma - Mobile Interface v1.0

## Component Library / Design System

### 设计系统方法

**设计系统方法：** 基于 Ant Design 构建定制化的科研专用组件库，确保一致性和可维护性

### 核心组件

#### 搜索组件 (SearchBox)

**目的：** 提供智能搜索和自动建议功能

**变体：**
- 全局搜索（带语音输入）
- 页面内搜索（简化版）
- 移动端搜索（大按钮版）

**状态：** 默认、聚焦、加载中、错误、禁用

**使用指南：** 始终提供搜索建议，支持键盘快捷键，移动端优化触摸体验

#### 试剂卡片 (ReagentCard)

**目的：** 统一展示试剂信息的核心组件

**变体：**
- 列表视图（紧凑信息）
- 网格视图（图片突出）
- 详情视图（完整信息）
- 移动端视图（垂直布局）

**状态：** 正常、悬停、选中、低库存警告、缺货

**使用指南：** 库存状态用颜色编码，支持快速操作按钮，响应式布局

#### 库存状态指示器 (StockIndicator)

**目的：** 直观显示库存状态和数量信息

**变体：**
- 数字显示（精确数量）
- 进度条（相对库存）
- 状态徽章（简化状态）
- 图标指示（极简模式）

**状态：** 充足（绿色）、偏低（橙色）、缺货（红色）、未知（灰色）

**使用指南：** 颜色和图标保持一致，支持国际化，考虑色盲用户

#### 操作按钮组 (ActionButtonGroup)

**目的：** 提供一致的操作入口和权限控制

**变体：**
- 主要操作（入库、出库）
- 次要操作（编辑、删除）
- 批量操作（全选、批量处理）

**状态：** 可用、禁用、加载中、成功、错误

**使用指南：** 根据用户权限显示，提供操作确认，支持键盘操作

#### 数据表格 (DataTable)

**目的：** 高效展示和操作大量数据

**变体：**
- 基础表格（简单数据）
- 可编辑表格（内联编辑）
- 树形表格（层级数据）
- 虚拟滚动表格（大数据量）

**状态：** 加载中、空数据、错误、选中行

**使用指南：** 支持排序筛选、列宽调整、数据导出、移动端横滚

## Branding & Style Guide

### 视觉识别

**品牌指南：** 遵循科研环境的专业性要求，体现现代化和可信赖的品牌形象

### 色彩方案

| 色彩类型 | 十六进制代码 | 使用场景 |
|---------|-------------|----------|
| 主色调 | #1890FF | 主要按钮、链接、品牌元素 |
| 次要色 | #722ED1 | 次要按钮、辅助信息 |
| 强调色 | #13C2C2 | 数据可视化、状态指示 |
| 成功色 | #52C41A | 库存充足、操作成功 |
| 警告色 | #FAAD14 | 库存偏低、重要提醒 |
| 错误色 | #F5222D | 缺货状态、错误信息 |
| 中性色 | #8C8C8C | 文本、边框、背景 |

### 字体系统

#### 字体族

- **主字体：** -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif
- **次要字体：** 'SF Pro Display', 'PingFang SC', 'Microsoft YaHei', sans-serif
- **等宽字体：** 'SF Mono', Monaco, 'Cascadia Code', monospace

#### 字体层级

| 元素 | 大小 | 字重 | 行高 |
|------|------|------|------|
| H1 | 32px | 600 | 1.2 |
| H2 | 24px | 600 | 1.3 |
| H3 | 20px | 500 | 1.4 |
| 正文 | 14px | 400 | 1.5 |
| 小字 | 12px | 400 | 1.4 |

### 图标系统

**图标库：** Ant Design Icons + 自定义科研图标

**使用指南：** 保持24px基准尺寸，使用线性风格，确保在不同背景下的可识别性

### 间距与布局

**网格系统：** 24列响应式网格，基于8px间距单位

**间距比例：** 8px, 16px, 24px, 32px, 48px, 64px

## Accessibility Requirements

### 合规目标

**标准：** WCAG 2.1 AA级别合规

### 关键要求

**视觉要求：**
- 颜色对比度：正文4.5:1，大文本3:1
- 焦点指示器：2px蓝色边框，清晰可见
- 文字大小：支持200%缩放而不影响功能

**交互要求：**
- 键盘导航：所有功能可通过键盘访问
- 屏幕阅读器：完整的ARIA标签和语义化HTML
- 触摸目标：最小44px×44px点击区域

**内容要求：**
- 替代文本：所有图片和图标提供描述性alt文本
- 标题结构：逻辑清晰的H1-H6层级
- 表单标签：所有输入字段有明确的label关联

### 测试策略

定期使用axe-core自动化测试，结合手动键盘导航和屏幕阅读器测试

## Responsiveness Strategy

### 断点设置

| 断点 | 最小宽度 | 最大宽度 | 目标设备 |
|------|----------|----------|----------|
| 手机 | 320px | 767px | iPhone, Android手机 |
| 平板 | 768px | 1023px | iPad, Android平板 |
| 桌面 | 1024px | 1439px | 笔记本电脑 |
| 宽屏 | 1440px | - | 台式机、大屏显示器 |

### 适配模式

**布局变化：**
- 桌面：多列布局，侧边栏导航
- 平板：两列布局，可折叠侧边栏
- 手机：单列布局，底部导航

**导航变化：**
- 桌面：顶部水平导航 + 左侧边栏
- 平板：顶部导航 + 抽屉式侧边栏
- 手机：底部标签导航 + 汉堡菜单

**内容优先级：**
- 核心功能（搜索、查看）在所有设备上优先显示
- 次要功能（批量操作、高级筛选）在小屏幕上折叠
- 装饰性元素在移动端隐藏或简化

**交互变化：**
- 桌面：鼠标悬停效果、右键菜单
- 触摸设备：手势操作、长按菜单、滑动刷新

## Animation & Micro-interactions

### 动效原则

遵循"有意义的动效"原则：每个动画都应该有明确的功能目的，提升用户理解和操作效率

### 关键动效

- **页面转场**：300ms ease-out淡入淡出，提供空间连续性 (Duration: 300ms, Easing: ease-out)
- **搜索建议**：150ms ease-in-out下拉展开，即时反馈用户输入 (Duration: 150ms, Easing: ease-in-out)
- **状态变化**：200ms ease-in颜色渐变，清晰传达状态更新 (Duration: 200ms, Easing: ease-in)
- **加载动画**：1000ms linear旋转，保持用户注意力 (Duration: 1000ms, Easing: linear)
- **成功反馈**：500ms bounce弹跳效果，增强正面情感 (Duration: 500ms, Easing: bounce)
- **错误提示**：300ms shake摇摆动画，引起注意但不过于突兀 (Duration: 300ms, Easing: shake)

## Performance Considerations

### 性能目标

- **页面加载**：首屏加载时间 < 2秒
- **交互响应**：用户操作响应时间 < 100ms
- **动画帧率**：保持60fps流畅动画

### 设计策略

实施渐进式加载、图片懒加载、组件按需加载等策略，优先保证核心功能的快速响应

## Next Steps

### 即时行动

1. **创建Figma设计文件**：建立完整的设计系统和页面原型
2. **用户测试计划**：制定可用性测试方案，验证设计假设
3. **技术可行性验证**：与开发团队确认技术实现方案
4. **无障碍审查**：进行初步的无障碍设计检查

### 设计交接清单

- [x] 所有用户流程已文档化
- [x] 组件库清单已完成
- [x] 无障碍要求已定义
- [x] 响应式策略已明确
- [x] 品牌指南已整合
- [x] 性能目标已建立

---

**前端规格说明完成！** 🎨

本文档为耗材库管理web应用提供了完整的UX/UI设计规格，涵盖信息架构、用户流程、组件设计、视觉规范等各个方面，已准备好进入详细的视觉设计和前端开发阶段。
