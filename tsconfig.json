{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@haocai/shared/*": ["packages/shared/src/*"], "@haocai/ui/*": ["packages/ui/src/*"], "@haocai/config/*": ["packages/config/src/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules", "dist", ".next", "coverage"], "references": [{"path": "./apps/web"}, {"path": "./apps/api"}, {"path": "./packages/shared"}, {"path": "./packages/ui"}, {"path": "./packages/config"}]}