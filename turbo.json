{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^build"]}, "type-check": {"dependsOn": ["^build"]}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "test:coverage": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "clean": {"cache": false}, "db:generate": {"cache": false}, "db:push": {"cache": false}, "db:migrate": {"cache": false}}}