version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: haocai-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: haocai
      POSTGRES_USER: haocai_user
      POSTGRES_PASSWORD: haocai_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infrastructure/docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - haocai-network

  # Redis 缓存
  redis:
    image: redis:7.2-alpine
    container_name: haocai-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - haocai-network

  # 后端API服务 (开发环境)
  api:
    build:
      context: .
      dockerfile: apps/api/Dockerfile.dev
    container_name: haocai-api
    restart: unless-stopped
    environment:
      NODE_ENV: development
      DATABASE_URL: ******************************************************/haocai
      REDIS_URL: redis://redis:6379
    ports:
      - "3001:3001"
    volumes:
      - ./apps/api:/app/apps/api
      - ./packages:/app/packages
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    networks:
      - haocai-network

  # 前端Web应用 (开发环境)
  web:
    build:
      context: .
      dockerfile: apps/web/Dockerfile.dev
    container_name: haocai-web
    restart: unless-stopped
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_API_URL: http://localhost:3001
    ports:
      - "3000:3000"
    volumes:
      - ./apps/web:/app/apps/web
      - ./packages:/app/packages
      - /app/node_modules
    depends_on:
      - api
    networks:
      - haocai-network

volumes:
  postgres_data:
  redis_data:

networks:
  haocai-network:
    driver: bridge
