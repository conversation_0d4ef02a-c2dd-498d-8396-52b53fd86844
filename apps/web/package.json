{"name": "@haocai/web", "version": "1.0.0", "description": "试剂库存管理系统 - 前端应用", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "vitest", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "clean": "rm -rf .next dist"}, "dependencies": {"@prisma/client": "^6.13.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^4.36.0", "@trpc/client": "^10.45.0", "@trpc/next": "^10.45.2", "@trpc/react-query": "^10.45.0", "@trpc/server": "^10.45.2", "antd": "^5.26.7", "lodash-es": "^4.17.21", "next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.3.0", "zustand": "^4.4.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^14.1.0", "@types/lodash-es": "^4.17.12", "@types/node": "^20.10.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.0", "eslint": "^8.55.0", "eslint-config-next": "^14.0.0", "jsdom": "^23.0.0", "postcss": "^8.4.0", "typescript": "^5.3.0", "vitest": "^1.0.0"}}