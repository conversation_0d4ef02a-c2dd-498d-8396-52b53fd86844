{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "ES2022"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "composite": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/pages/*": ["./src/pages/*"], "@/hooks/*": ["./src/hooks/*"], "@/services/*": ["./src/services/*"], "@/stores/*": ["./src/stores/*"], "@/utils/*": ["./src/utils/*"], "@haocai/shared/*": ["../../packages/shared/src/*"], "@haocai/ui/*": ["../../packages/ui/src/*"], "@haocai/config/*": ["../../packages/config/src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "../../packages/shared/src/**/*.ts"], "exclude": ["node_modules", ".next", "dist"]}