import React, { useState, useCallback, useEffect } from 'react';
import { Input, AutoComplete, Typography } from 'antd';
import { SearchOutlined, ClockCircleOutlined, DeleteOutlined } from '@ant-design/icons';
import { debounce } from 'lodash-es';
import { useReagentStore } from '@/stores/reagentStore';

const { Text } = Typography;

/**
 * 搜索建议项接口
 */
interface SearchSuggestion {
  value: string;
  label: string;
  type: 'reagent' | 'supplier' | 'category';
}

/**
 * 搜索输入框组件属性
 */
interface SearchInputProps {
  value?: string;
  placeholder?: string;
  onSearch: (value: string) => void;
  onSuggestionSelect?: (value: string, option: SearchSuggestion) => void;
  suggestions?: SearchSuggestion[];
  loading?: boolean;
  disabled?: boolean;
  size?: 'small' | 'middle' | 'large';
  className?: string;
}

/**
 * 搜索输入框组件
 * 支持实时搜索建议和自动补全功能
 */
export const SearchInput: React.FC<SearchInputProps> = ({
  value = '',
  placeholder = '搜索试剂名称、编码或规格...',
  onSearch,
  onSuggestionSelect,
  suggestions = [],
  loading = false,
  disabled = false,
  size = 'large',
  className = ''
}) => {
  const [inputValue, setInputValue] = useState(value);
  const [options, setOptions] = useState<{ value: string; label: React.ReactNode }[]>([]);

  // 获取搜索历史
  const { getRecentSearches, removeSearchHistory, addSearchHistory } = useReagentStore();

  // 防抖搜索函数
  const debouncedSearch = useCallback(
    debounce((searchValue: string) => {
      if (searchValue.trim()) {
        onSearch(searchValue.trim());
      }
    }, 300),
    [onSearch]
  );

  // 处理输入变化
  const handleInputChange = useCallback((searchValue: string) => {
    setInputValue(searchValue);
    debouncedSearch(searchValue);
  }, [debouncedSearch]);

  // 处理搜索
  const handleSearch = useCallback((searchValue: string) => {
    const trimmedValue = searchValue.trim();
    if (trimmedValue) {
      // 添加到搜索历史
      addSearchHistory(trimmedValue);
      onSearch(trimmedValue);
    }
  }, [onSearch, addSearchHistory]);

  // 处理建议选择
  const handleSelect = useCallback((selectedValue: string, option: any) => {
    setInputValue(selectedValue);
    const suggestion = suggestions.find(s => s.value === selectedValue);
    if (suggestion && onSuggestionSelect) {
      onSuggestionSelect(selectedValue, suggestion);
    } else {
      onSearch(selectedValue);
    }
  }, [suggestions, onSuggestionSelect, onSearch]);

  // 处理删除搜索历史
  const handleRemoveHistory = useCallback((e: React.MouseEvent, historyId: string) => {
    e.stopPropagation();
    removeSearchHistory(historyId);
  }, [removeSearchHistory]);

  // 更新建议选项
  useEffect(() => {
    const recentSearches = getRecentSearches(5);
    const allOptions: { value: string; label: React.ReactNode }[] = [];

    // 添加搜索历史
    if (recentSearches.length > 0 && !inputValue.trim()) {
      allOptions.push({
        value: '__history_header__',
        label: (
          <div className="px-2 py-1 text-xs text-gray-500 font-medium border-b">
            <ClockCircleOutlined className="mr-1" />
            最近搜索
          </div>
        )
      });

      recentSearches.forEach(history => {
        allOptions.push({
          value: history.query,
          label: (
            <div className="flex items-center justify-between group">
              <div className="flex items-center">
                <ClockCircleOutlined className="mr-2 text-gray-400" />
                <span>{history.query}</span>
                {history.resultCount !== undefined && (
                  <Text type="secondary" className="text-xs ml-2">
                    ({history.resultCount} 个结果)
                  </Text>
                )}
              </div>
              <DeleteOutlined
                className="text-gray-400 hover:text-red-500 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={(e) => handleRemoveHistory(e, history.id)}
              />
            </div>
          )
        });
      });
    }

    // 添加搜索建议
    if (suggestions.length > 0) {
      if (allOptions.length > 0) {
        allOptions.push({
          value: '__suggestions_header__',
          label: (
            <div className="px-2 py-1 text-xs text-gray-500 font-medium border-b">
              <SearchOutlined className="mr-1" />
              搜索建议
            </div>
          )
        });
      }

      suggestions.forEach(suggestion => {
        allOptions.push({
          value: suggestion.value,
          label: (
            <div className="flex items-center justify-between">
              <span>{suggestion.label}</span>
              <span className="text-xs text-gray-400 ml-2">
                {suggestion.type === 'reagent' && '试剂'}
                {suggestion.type === 'supplier' && '供应商'}
                {suggestion.type === 'category' && '分类'}
              </span>
            </div>
          )
        });
      });
    }

    setOptions(allOptions);
  }, [suggestions, inputValue, getRecentSearches, handleRemoveHistory]);

  // 同步外部值变化
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  return (
    <div className={`search-input-wrapper ${className}`}>
      <AutoComplete
        value={inputValue}
        options={options}
        onSelect={handleSelect}
        onSearch={handleInputChange}
        className="w-full"
        size={size}
        disabled={disabled}
        notFoundContent={loading ? '搜索中...' : '暂无建议'}
        filterOption={(inputValue, option) => {
          // 过滤掉分隔符选项
          if (option?.value === '__history_header__' || option?.value === '__suggestions_header__') {
            return false;
          }
          return true;
        }}
      >
        <Input.Search
          placeholder={placeholder}
          enterButton={
            <div className="flex items-center">
              <SearchOutlined />
              <span className="ml-1 hidden sm:inline">搜索</span>
            </div>
          }
          size={size}
          loading={loading}
          disabled={disabled}
          onSearch={handleSearch}
          className="search-input"
        />
      </AutoComplete>
    </div>
  );
};
