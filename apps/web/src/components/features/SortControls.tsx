import React from 'react';
import { Select, Space, Typography } from 'antd';
import { SortAscendingOutlined, SortDescendingOutlined } from '@ant-design/icons';
import { SORT_FIELD_LABELS } from '@haocai/shared/types/reagent';

const { Text } = Typography;
const { Option } = Select;

/**
 * 排序控件组件属性
 */
interface SortControlsProps {
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  onSortChange: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  className?: string;
  size?: 'small' | 'middle' | 'large';
}

/**
 * 排序控件组件
 * 实现多字段排序（名称、编码、库存量、供应商）
 */
export const SortControls: React.FC<SortControlsProps> = ({
  sortBy,
  sortOrder,
  onSortChange,
  className = '',
  size = 'small'
}) => {
  // 处理排序字段变化
  const handleSortFieldChange = (newSortBy: string) => {
    onSortChange(newSortBy, sortOrder);
  };

  // 处理排序方向变化
  const handleSortOrderChange = (newSortOrder: 'asc' | 'desc') => {
    onSortChange(sortBy, newSortOrder);
  };

  // 获取排序字段选项
  const getSortFieldOptions = () => {
    return Object.entries(SORT_FIELD_LABELS).map(([value, label]) => (
      <Option key={value} value={value}>
        {label}
      </Option>
    ));
  };

  return (
    <div className={`sort-controls ${className}`}>
      <Space size="middle" wrap>
        <div className="flex items-center space-x-2">
          <Text className="text-sm text-gray-600 whitespace-nowrap">排序:</Text>
          <Select
            value={sortBy}
            onChange={handleSortFieldChange}
            size={size}
            className="min-w-[100px]"
          >
            {getSortFieldOptions()}
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <Text className="text-sm text-gray-600 whitespace-nowrap">方向:</Text>
          <Select
            value={sortOrder}
            onChange={handleSortOrderChange}
            size={size}
            className="min-w-[100px]"
          >
            <Option value="asc">
              <Space>
                <SortAscendingOutlined />
                <span>升序</span>
              </Space>
            </Option>
            <Option value="desc">
              <Space>
                <SortDescendingOutlined />
                <span>降序</span>
              </Space>
            </Option>
          </Select>
        </div>
      </Space>
    </div>
  );
};
