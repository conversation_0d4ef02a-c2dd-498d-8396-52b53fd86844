import React from 'react';
import { Card, Tag, Descriptions, Space, Typography } from 'antd';
import { CalendarOutlined, UserOutlined } from '@ant-design/icons';
import { ReagentWithStatus } from '@haocai/shared/types/reagent';
import { CATEGORY_LABELS } from '@haocai/shared/types/reagent';
import { formatDate } from '@/utils/format';

const { Title, Text } = Typography;

interface ReagentDetailCardProps {
  reagent: ReagentWithStatus;
}

/**
 * 试剂基本信息卡片组件
 * 展示试剂的核心信息，包括名称、编码、分类等
 */
export const ReagentDetailCard: React.FC<ReagentDetailCardProps> = ({ reagent }) => {
  // 获取分类显示名称
  const getCategoryLabel = (category: string) => {
    return CATEGORY_LABELS[category] || category;
  };

  // 获取分类标签颜色
  const getCategoryColor = (category: string) => {
    const colorMap: Record<string, string> = {
      'BIOLOGICAL_REAGENT': 'blue',
      'LAB_CONSUMABLE': 'green',
      'CULTURE_MEDIUM': 'orange',
    };
    return colorMap[category] || 'default';
  };

  return (
    <Card
      title={
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
          <Title level={4} className="!mb-0">
            基本信息
          </Title>
          <Tag
            color={getCategoryColor(reagent.category)}
            className="text-sm"
          >
            {getCategoryLabel(reagent.category)}
          </Tag>
        </div>
      }
      className="shadow-sm"
    >
      <Descriptions
        column={{ xs: 1, sm: 2, md: 2, lg: 2 }}
        styles={{
          label: { fontWeight: 500, color: '#666' },
          content: { color: '#333' }
        }}
      >
        <Descriptions.Item label="试剂名称">
          <Text strong className="text-base">
            {reagent.name}
          </Text>
        </Descriptions.Item>

        <Descriptions.Item label="试剂编码">
          <Text code className="text-sm bg-gray-100 px-2 py-1 rounded">
            {reagent.code}
          </Text>
        </Descriptions.Item>

        <Descriptions.Item label="规格">
          <Text>{reagent.specification || '未指定'}</Text>
        </Descriptions.Item>

        <Descriptions.Item label="单位">
          <Text>{reagent.unit}</Text>
        </Descriptions.Item>

        <Descriptions.Item label="供应商">
          <Text>{reagent.supplier || '未指定'}</Text>
        </Descriptions.Item>

        <Descriptions.Item label="生产厂家">
          <Text>{reagent.manufacturer || '未指定'}</Text>
        </Descriptions.Item>

        {reagent.casNumber && (
          <Descriptions.Item label="CAS号">
            <Text code className="text-sm">
              {reagent.casNumber}
            </Text>
          </Descriptions.Item>
        )}

        {reagent.formula && (
          <Descriptions.Item label="分子式">
            <Text code className="text-sm">
              {reagent.formula}
            </Text>
          </Descriptions.Item>
        )}

        {reagent.molecularWeight && (
          <Descriptions.Item label="分子量">
            <Text>{reagent.molecularWeight}</Text>
          </Descriptions.Item>
        )}

        {reagent.purity && (
          <Descriptions.Item label="纯度">
            <Text>{reagent.purity}</Text>
          </Descriptions.Item>
        )}

        {reagent.unitPrice && (
          <Descriptions.Item label="单价">
            <Text>¥{reagent.unitPrice}</Text>
          </Descriptions.Item>
        )}

        {reagent.location && (
          <Descriptions.Item label="存放位置">
            <Text>{reagent.location}</Text>
          </Descriptions.Item>
        )}

        <Descriptions.Item label="创建时间">
          <Space>
            <CalendarOutlined className="text-gray-400" />
            <Text className="text-sm">
              {formatDate(reagent.createdAt)}
            </Text>
          </Space>
        </Descriptions.Item>

        <Descriptions.Item label="更新时间">
          <Space>
            <CalendarOutlined className="text-gray-400" />
            <Text className="text-sm">
              {formatDate(reagent.updatedAt)}
            </Text>
          </Space>
        </Descriptions.Item>

        {reagent.createdBy && (
          <Descriptions.Item label="创建人">
            <Space>
              <UserOutlined className="text-gray-400" />
              <Text className="text-sm">
                {reagent.createdBy}
              </Text>
            </Space>
          </Descriptions.Item>
        )}

        {reagent.description && (
          <Descriptions.Item label="描述" span={2}>
            <Text className="text-sm text-gray-600">
              {reagent.description}
            </Text>
          </Descriptions.Item>
        )}
      </Descriptions>
    </Card>
  );
};
