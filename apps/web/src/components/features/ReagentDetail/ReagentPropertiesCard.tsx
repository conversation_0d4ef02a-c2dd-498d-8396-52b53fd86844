import React from 'react';
import { Card, Descriptions, Tag, Space, Typography, Divider } from 'antd';
import {
  SafetyOutlined,
  ExperimentOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { ReagentWithStatus } from '@haocai/shared/types/reagent';

const { Title, Text } = Typography;

interface ReagentPropertiesCardProps {
  reagent: ReagentWithStatus;
}

/**
 * 试剂详细属性卡片组件
 * 展示试剂的化学属性、安全信息和存储条件等详细信息
 */
export const ReagentPropertiesCard: React.FC<ReagentPropertiesCardProps> = ({ reagent }) => {
  // 获取安全等级颜色
  const getSafetyLevelColor = (level: string | null) => {
    if (!level) return 'default';

    const levelMap: Record<string, string> = {
      '低': 'green',
      '中': 'orange',
      '高': 'red',
      '极高': 'red',
      'low': 'green',
      'medium': 'orange',
      'high': 'red',
      'critical': 'red'
    };

    return levelMap[level.toLowerCase()] || 'default';
  };

  // 格式化存储条件
  const formatStorageCondition = (condition: string | null) => {
    if (!condition) return '常温保存';
    return condition;
  };

  return (
    <Card
      title={
        <div className="flex items-center gap-2">
          <InfoCircleOutlined />
          <Title level={4} className="!mb-0">
            详细属性
          </Title>
        </div>
      }
      className="shadow-sm h-fit"
    >
      {/* 化学属性部分 */}
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-3">
          <ExperimentOutlined className="text-blue-500" />
          <Text strong>化学属性</Text>
        </div>

        <Descriptions
          column={1}
          size="small"
          styles={{
            label: { fontWeight: 500, color: '#666', width: '80px' },
            content: { color: '#333' }
          }}
        >
          {reagent.casNumber && (
            <Descriptions.Item label="CAS号">
              <Text code className="text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded">
                {reagent.casNumber}
              </Text>
            </Descriptions.Item>
          )}

          {reagent.formula && (
            <Descriptions.Item label="分子式">
              <Text code className="text-xs bg-green-50 text-green-700 px-2 py-1 rounded">
                {reagent.formula}
              </Text>
            </Descriptions.Item>
          )}

          {reagent.molecularWeight && (
            <Descriptions.Item label="分子量">
              <Text>{reagent.molecularWeight}</Text>
            </Descriptions.Item>
          )}

          {reagent.purity && (
            <Descriptions.Item label="纯度">
              <Tag color="blue" className="text-xs">
                {reagent.purity}
              </Tag>
            </Descriptions.Item>
          )}
        </Descriptions>
      </div>

      <Divider className="my-4" />

      {/* 安全信息部分 */}
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-3">
          <SafetyOutlined className="text-red-500" />
          <Text strong>安全信息</Text>
        </div>

        <Descriptions
          column={1}
          size="small"
          styles={{
            label: { fontWeight: 500, color: '#666', width: '80px' },
            content: { color: '#333' }
          }}
        >
          <Descriptions.Item label="安全等级">
            {reagent.safetyLevel ? (
              <Tag
                color={getSafetyLevelColor(reagent.safetyLevel)}
                className="text-xs"
              >
                {reagent.safetyLevel}
              </Tag>
            ) : (
              <Text className="text-gray-400 text-xs">未指定</Text>
            )}
          </Descriptions.Item>
        </Descriptions>
      </div>

      <Divider className="my-4" />

      {/* 存储信息部分 */}
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-3">
          <EnvironmentOutlined className="text-green-500" />
          <Text strong>存储信息</Text>
        </div>

        <Descriptions
          column={1}
          size="small"
          styles={{
            label: { fontWeight: 500, color: '#666', width: '80px' },
            content: { color: '#333' }
          }}
        >
          <Descriptions.Item label="存储条件">
            <Tag color="green" className="text-xs">
              {formatStorageCondition(reagent.storageCondition)}
            </Tag>
          </Descriptions.Item>

          {reagent.location && (
            <Descriptions.Item label="存放位置">
              <Space>
                <EnvironmentOutlined className="text-gray-400 text-xs" />
                <Text className="text-sm">{reagent.location}</Text>
              </Space>
            </Descriptions.Item>
          )}
        </Descriptions>
      </div>

      <Divider className="my-4" />

      {/* 价格信息部分 */}
      {(reagent.unitPrice || reagent.totalPrice) && (
        <div>
          <div className="flex items-center gap-2 mb-3">
            <InfoCircleOutlined className="text-purple-500" />
            <Text strong>价格信息</Text>
          </div>

          <Descriptions
            column={1}
            size="small"
            styles={{
              label: { fontWeight: 500, color: '#666', width: '80px' },
              content: { color: '#333' }
            }}
          >
            {reagent.unitPrice && (
              <Descriptions.Item label="单价">
                <Text strong className="text-green-600">
                  ¥{reagent.unitPrice}
                </Text>
                <Text className="text-gray-400 text-xs ml-1">
                  / {reagent.unit}
                </Text>
              </Descriptions.Item>
            )}

            {reagent.totalPrice && (
              <Descriptions.Item label="总价值">
                <Text strong className="text-blue-600">
                  ¥{reagent.totalPrice}
                </Text>
              </Descriptions.Item>
            )}
          </Descriptions>
        </div>
      )}

      {/* 状态信息 */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <div className="flex items-center justify-between">
          <Text className="text-xs text-gray-500">状态</Text>
          <Tag
            color={reagent.isActive ? 'green' : 'red'}
            className="text-xs"
          >
            {reagent.isActive ? '启用' : '禁用'}
          </Tag>
        </div>
      </div>
    </Card>
  );
};
