import React from 'react';
import { Card, Row, Col, Statistic, Progress, Tag, Alert, Space, Typography } from 'antd';
import { 
  StockOutlined, 
  WarningOutlined, 
  CheckCircleOutlined,
  ExclamationCircleOutlined 
} from '@ant-design/icons';
import { ReagentWithStatus, StockStatus, STOCK_STATUS_LABELS, STOCK_STATUS_COLORS } from '@haocai/shared/types/reagent';

const { Title, Text } = Typography;

interface StockInfoCardProps {
  reagent: ReagentWithStatus;
}

/**
 * 库存信息卡片组件
 * 展示试剂的库存状态、数量和相关指标
 */
export const StockInfoCard: React.FC<StockInfoCardProps> = ({ reagent }) => {
  // 计算库存百分比
  const calculateStockPercentage = () => {
    const current = Number(reagent.currentStock);
    const max = Number(reagent.maxCapacity);
    
    if (!max || max <= 0) return 0;
    return Math.min((current / max) * 100, 100);
  };

  // 计算可出库数量（当前库存减去最小阈值）
  const calculateAvailableStock = () => {
    const current = Number(reagent.currentStock);
    const min = Number(reagent.minThreshold);
    return Math.max(current - min, 0);
  };

  // 获取库存状态图标
  const getStockStatusIcon = (status: StockStatus) => {
    switch (status) {
      case StockStatus.OUT_OF_STOCK:
        return <ExclamationCircleOutlined className="text-red-500" />;
      case StockStatus.LOW_STOCK:
        return <WarningOutlined className="text-orange-500" />;
      case StockStatus.IN_STOCK:
        return <CheckCircleOutlined className="text-green-500" />;
      default:
        return <StockOutlined />;
    }
  };

  // 获取进度条颜色
  const getProgressColor = (status: StockStatus) => {
    switch (status) {
      case StockStatus.OUT_OF_STOCK:
        return '#ff4d4f';
      case StockStatus.LOW_STOCK:
        return '#faad14';
      case StockStatus.IN_STOCK:
        return '#52c41a';
      default:
        return '#1890ff';
    }
  };

  // 获取库存警告信息
  const getStockAlert = () => {
    const current = Number(reagent.currentStock);
    const min = Number(reagent.minThreshold);
    
    if (current <= 0) {
      return {
        type: 'error' as const,
        message: '库存已耗尽',
        description: '当前库存为0，请及时补充库存'
      };
    }
    
    if (current <= min) {
      return {
        type: 'warning' as const,
        message: '库存不足',
        description: `当前库存 ${current} ${reagent.unit}，已低于最小阈值 ${min} ${reagent.unit}`
      };
    }
    
    return null;
  };

  const stockPercentage = calculateStockPercentage();
  const availableStock = calculateAvailableStock();
  const stockAlert = getStockAlert();

  return (
    <Card
      title={
        <div className="flex items-center gap-2">
          <StockOutlined />
          <Title level={4} className="!mb-0">
            库存信息
          </Title>
        </div>
      }
      className="shadow-sm"
    >
      {/* 库存状态警告 */}
      {stockAlert && (
        <Alert
          message={stockAlert.message}
          description={stockAlert.description}
          type={stockAlert.type}
          showIcon
          className="mb-4"
        />
      )}

      {/* 库存统计数据 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={12} sm={6}>
          <Statistic
            title="当前库存"
            value={reagent.currentStock}
            suffix={reagent.unit}
            valueStyle={{ color: getProgressColor(reagent.stockStatus) }}
            prefix={getStockStatusIcon(reagent.stockStatus)}
          />
        </Col>
        
        <Col xs={12} sm={6}>
          <Statistic
            title="可出库数量"
            value={availableStock}
            suffix={reagent.unit}
            valueStyle={{ 
              color: availableStock > 0 ? '#52c41a' : '#ff4d4f' 
            }}
          />
        </Col>
        
        <Col xs={12} sm={6}>
          <Statistic
            title="最小阈值"
            value={reagent.minThreshold}
            suffix={reagent.unit}
            valueStyle={{ color: '#666' }}
          />
        </Col>
        
        <Col xs={12} sm={6}>
          <Statistic
            title="最大容量"
            value={reagent.maxCapacity || '未设置'}
            suffix={reagent.maxCapacity ? reagent.unit : ''}
            valueStyle={{ color: '#666' }}
          />
        </Col>
      </Row>

      {/* 库存状态标签和进度条 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <Text strong>库存状态</Text>
          <Tag 
            color={STOCK_STATUS_COLORS[reagent.stockStatus]}
            icon={getStockStatusIcon(reagent.stockStatus)}
          >
            {STOCK_STATUS_LABELS[reagent.stockStatus]}
          </Tag>
        </div>

        {/* 库存进度条（仅在设置了最大容量时显示） */}
        {reagent.maxCapacity && Number(reagent.maxCapacity) > 0 && (
          <div>
            <div className="flex items-center justify-between mb-2">
              <Text className="text-sm text-gray-600">库存占用率</Text>
              <Text className="text-sm text-gray-600">
                {stockPercentage.toFixed(1)}%
              </Text>
            </div>
            <Progress
              percent={stockPercentage}
              strokeColor={getProgressColor(reagent.stockStatus)}
              showInfo={false}
              size="small"
            />
          </div>
        )}

        {/* 最近交易信息 */}
        {reagent.lastTransaction && (
          <div className="pt-4 border-t border-gray-100">
            <Text className="text-sm text-gray-600">
              最近操作: {reagent.lastTransaction.type} - {' '}
              {new Date(reagent.lastTransaction.createdAt).toLocaleDateString()}
            </Text>
          </div>
        )}
      </div>
    </Card>
  );
};
