import React from 'react';
import { Card, Tag, Typography, Space, Tooltip, Button } from 'antd';
import {
  ExperimentOutlined,
  ShopOutlined,
  EnvironmentOutlined,
  EyeOutlined,
  EditOutlined
} from '@ant-design/icons';
import {
  ReagentWithStatus,
  CATEGORY_LABELS,
  STOCK_STATUS_LABELS,
  STOCK_STATUS_COLORS
} from '@haocai/shared/types/reagent';

const { Text, Title } = Typography;

/**
 * 试剂卡片组件属性
 */
interface ReagentCardProps {
  reagent: ReagentWithStatus;
  onView?: (reagent: ReagentWithStatus) => void;
  onEdit?: (reagent: ReagentWithStatus) => void;
  className?: string;
  size?: 'small' | 'default';
}

/**
 * 试剂卡片组件
 * 以卡片形式展示试剂关键信息
 */
export const ReagentCard: React.FC<ReagentCardProps> = ({
  reagent,
  onView,
  onEdit,
  className = '',
  size = 'default'
}) => {
  // 格式化库存显示
  const formatStock = (stock: string | number, unit: string) => {
    return `${stock} ${unit}`;
  };

  // 格式化价格显示
  const formatPrice = (price: string | number | null) => {
    if (!price) return '-';
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return `¥${numPrice.toFixed(2)}`;
  };

  // 获取库存状态标签
  const getStockStatusTag = () => {
    const { stockStatus } = reagent;
    return (
      <Tag color={STOCK_STATUS_COLORS[stockStatus]} className="mb-0">
        {STOCK_STATUS_LABELS[stockStatus]}
      </Tag>
    );
  };

  // 卡片操作按钮
  const actions = [
    onView && (
      <Tooltip title="查看详情" key="view">
        <Button
          type="text"
          icon={<EyeOutlined />}
          onClick={() => onView(reagent)}
          size="small"
        >
          {size === 'default' && '查看'}
        </Button>
      </Tooltip>
    ),
    onEdit && (
      <Tooltip title="编辑" key="edit">
        <Button
          type="text"
          icon={<EditOutlined />}
          onClick={() => onEdit(reagent)}
          size="small"
        >
          {size === 'default' && '编辑'}
        </Button>
      </Tooltip>
    )
  ].filter(Boolean);

  return (
    <Card
      className={`reagent-card ${className} ${size === 'small' ? 'compact' : ''}`}
      size={size}
      actions={actions.length > 0 ? actions : undefined}
      hoverable
    >
      {/* 卡片头部 */}
      <div className="flex justify-between items-start mb-3">
        <div className="flex-1 min-w-0">
          <Title
            level={size === 'small' ? 5 : 4}
            className="mb-1 truncate"
            title={reagent.name}
          >
            {reagent.name}
          </Title>
          <Text type="secondary" className="text-sm">
            编码: {reagent.code}
          </Text>
        </div>
        <div className="ml-2 flex-shrink-0">
          {getStockStatusTag()}
        </div>
      </div>

      {/* 基本信息 */}
      <Space direction="vertical" size="small" className="w-full">
        {/* 规格和分类 */}
        <div className="flex justify-between items-center">
          <Text className="text-sm">
            <ExperimentOutlined className="mr-1" />
            {reagent.specification || '未指定规格'}
          </Text>
          <Tag color="blue" className="text-xs">
            {CATEGORY_LABELS[reagent.category]}
          </Tag>
        </div>

        {/* 供应商 */}
        {reagent.supplier && (
          <Text className="text-sm text-gray-600">
            <ShopOutlined className="mr-1" />
            {reagent.supplier}
          </Text>
        )}

        {/* 存储位置 */}
        {reagent.location && (
          <Text className="text-sm text-gray-600">
            <EnvironmentOutlined className="mr-1" />
            {reagent.location}
          </Text>
        )}

        {/* 库存信息 */}
        <div className="flex justify-between items-center pt-2 border-t border-gray-100">
          <div>
            <Text strong className="text-sm">
              库存: {formatStock(reagent.currentStock, reagent.unit)}
            </Text>
            {Number(reagent.minThreshold) > 0 && (
              <Text type="secondary" className="text-xs ml-2">
                (警戒线: {reagent.minThreshold})
              </Text>
            )}
          </div>
          {reagent.unitPrice && (
            <Text className="text-sm font-medium">
              {formatPrice(reagent.unitPrice)}
            </Text>
          )}
        </div>

        {/* 存储条件 */}
        {reagent.storageCondition && size === 'default' && (
          <Text className="text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded">
            存储: {reagent.storageCondition}
          </Text>
        )}
      </Space>

      {/* 最后交易信息 */}
      {reagent.lastTransaction && size === 'default' && (
        <div className="mt-3 pt-2 border-t border-gray-100">
          <Text type="secondary" className="text-xs">
            最后操作: {reagent.lastTransaction.type} • {' '}
            {new Date(reagent.lastTransaction.createdAt).toLocaleDateString()}
          </Text>
        </div>
      )}
    </Card>
  );
};
