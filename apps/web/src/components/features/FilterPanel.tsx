import React from 'react';
import { Card, Select, Switch, Button, Space, Typography, Divider, Tag } from 'antd';
import { ClearOutlined, FilterOutlined } from '@ant-design/icons';
import {
  CATEGORY_LABELS
} from '@haocai/shared/types/reagent';

const { Text } = Typography;
const { Option } = Select;

/**
 * 筛选器面板组件属性
 */
interface FilterPanelProps {
  // 当前筛选值
  category?: string;
  supplier?: string;
  lowStock: boolean;

  // 筛选器数据
  categoryStats: Record<string, number>;
  suppliers: string[];

  // 事件处理
  onCategoryChange: (category?: string) => void;
  onSupplierChange: (supplier?: string) => void;
  onLowStockChange: (lowStock: boolean) => void;
  onReset: () => void;

  // 样式
  className?: string;
  collapsed?: boolean;
}

/**
 * 筛选器面板组件
 * 提供分类、状态等多维度筛选选项
 */
export const FilterPanel: React.FC<FilterPanelProps> = ({
  category,
  supplier,
  lowStock,
  categoryStats,
  suppliers,
  onCategoryChange,
  onSupplierChange,
  onLowStockChange,
  onReset,
  className = '',
  collapsed = false
}) => {
  // 计算活跃筛选器数量
  const activeFiltersCount = [
    category,
    supplier,
    lowStock
  ].filter(Boolean).length;

  // 获取分类选项
  const getCategoryOptions = () => {
    return Object.entries(CATEGORY_LABELS).map(([value, label]) => {
      const count = categoryStats[value] || 0;
      return (
        <Option key={value} value={value}>
          <div className="flex justify-between items-center">
            <span>{label}</span>
            <Tag color="blue" className="ml-2 text-xs">
              {count}
            </Tag>
          </div>
        </Option>
      );
    });
  };

  // 获取供应商选项
  const getSupplierOptions = () => {
    return suppliers.map(supplierName => (
      <Option key={supplierName} value={supplierName}>
        {supplierName}
      </Option>
    ));
  };

  if (collapsed) {
    return (
      <div className={`filter-panel-collapsed ${className}`}>
        <Space wrap>
          <Button
            icon={<FilterOutlined />}
            type={activeFiltersCount > 0 ? 'primary' : 'default'}
          >
            筛选 {activeFiltersCount > 0 && `(${activeFiltersCount})`}
          </Button>

          {/* 活跃筛选器标签 */}
          {category && (
            <Tag
              closable
              onClose={() => onCategoryChange(undefined)}
              color="blue"
            >
              {CATEGORY_LABELS[category]}
            </Tag>
          )}

          {supplier && (
            <Tag
              closable
              onClose={() => onSupplierChange(undefined)}
              color="green"
            >
              {supplier}
            </Tag>
          )}

          {lowStock && (
            <Tag
              closable
              onClose={() => onLowStockChange(false)}
              color="orange"
            >
              低库存
            </Tag>
          )}

          {activeFiltersCount > 0 && (
            <Button
              type="text"
              size="small"
              icon={<ClearOutlined />}
              onClick={onReset}
            >
              清除
            </Button>
          )}
        </Space>
      </div>
    );
  }

  return (
    <Card
      className={`filter-panel ${className}`}
      size="small"
      title={
        <div className="flex justify-between items-center">
          <Space>
            <FilterOutlined />
            <span>筛选器</span>
            {activeFiltersCount > 0 && (
              <Tag color="primary" className="text-xs">
                {activeFiltersCount}
              </Tag>
            )}
          </Space>
          {activeFiltersCount > 0 && (
            <Button
              type="text"
              size="small"
              icon={<ClearOutlined />}
              onClick={onReset}
            >
              重置
            </Button>
          )}
        </div>
      }
    >
      <Space direction="vertical" size="middle" className="w-full">
        {/* 分类筛选 */}
        <div>
          <Text strong className="block mb-2">试剂分类</Text>
          <Select
            placeholder="选择分类"
            value={category}
            onChange={onCategoryChange}
            allowClear
            className="w-full"
            size="small"
          >
            {getCategoryOptions()}
          </Select>
        </div>

        <Divider className="my-3" />

        {/* 供应商筛选 */}
        <div>
          <Text strong className="block mb-2">供应商</Text>
          <Select
            placeholder="选择供应商"
            value={supplier}
            onChange={onSupplierChange}
            allowClear
            showSearch
            filterOption={(input, option) =>
              String(option?.children || '').toLowerCase().includes(input.toLowerCase())
            }
            className="w-full"
            size="small"
          >
            {getSupplierOptions()}
          </Select>
        </div>

        <Divider className="my-3" />

        {/* 库存状态筛选 */}
        <div>
          <div className="flex justify-between items-center">
            <Text strong>仅显示低库存</Text>
            <Switch
              checked={lowStock}
              onChange={onLowStockChange}
              size="small"
            />
          </div>
          <Text type="secondary" className="text-xs mt-1 block">
            显示库存低于警戒线的试剂
          </Text>
        </div>
      </Space>
    </Card>
  );
};
