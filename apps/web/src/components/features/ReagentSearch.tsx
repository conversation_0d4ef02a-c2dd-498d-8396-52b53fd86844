import React, { useState, useMemo, useRef } from 'react';
import {
  Layout,
  Row,
  Col,
  Pagination,
  Spin,
  Empty,
  Alert,
  Typography,
  Space,
  Button,
  Drawer,
  Tooltip
} from 'antd';
import { FilterOutlined, AppstoreOutlined, BarsOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { SearchInput } from '@/components/ui/SearchInput';
import { FilterPanel } from './FilterPanel';
import { SortControls } from './SortControls';
import { ReagentCard } from './ReagentCard';
import { useReagentSearch } from '@/hooks/useReagentSearch';
import { useKeyboardShortcuts, formatShortcut } from '@/hooks/useKeyboardShortcuts';
import { ReagentWithStatus, CATEGORY_LABELS } from '@haocai/shared/types/reagent';

const { Content, Sider } = Layout;
const { Title, Text } = Typography;

/**
 * 搜索界面组件属性
 */
interface ReagentSearchProps {
  onReagentView?: (reagent: ReagentWithStatus) => void;
  onReagentEdit?: (reagent: ReagentWithStatus) => void;
  className?: string;
}

/**
 * 试剂搜索界面组件
 * 整合搜索框、筛选器、结果列表等功能
 */
export const ReagentSearch: React.FC<ReagentSearchProps> = ({
  onReagentView,
  onReagentEdit,
  className = ''
}) => {
  // 移动端筛选器抽屉状态
  const [filterDrawerVisible, setFilterDrawerVisible] = useState(false);

  // 视图模式状态
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // 搜索输入框引用
  const searchInputRef = useRef<any>(null);

  // 使用搜索 Hook
  const {
    searchState,
    reagents,
    total,
    loading,
    error,
    categoryStats,
    popularKeywords,
    setQuery,
    setCategory,
    setSupplier,
    setLowStock,
    setPage,
    setPageSize,
    setSorting,
    resetFilters,
    totalPages
  } = useReagentSearch();

  // 键盘快捷键配置
  const shortcuts = useKeyboardShortcuts([
    {
      key: 'k',
      ctrlKey: true,
      callback: () => {
        // 聚焦搜索框
        if (searchInputRef.current) {
          const input = searchInputRef.current.querySelector('input');
          if (input) {
            input.focus();
            input.select();
          }
        }
      },
      description: '聚焦搜索框'
    },
    {
      key: 'f',
      ctrlKey: true,
      callback: () => {
        // 打开筛选器
        setFilterDrawerVisible(true);
      },
      description: '打开筛选器'
    },
    {
      key: 'g',
      callback: () => {
        // 切换网格视图
        setViewMode('grid');
      },
      description: '网格视图'
    },
    {
      key: 'l',
      callback: () => {
        // 切换列表视图
        setViewMode('list');
      },
      description: '列表视图'
    },
    {
      key: 'r',
      callback: () => {
        // 重置筛选器
        resetFilters();
      },
      description: '重置筛选器'
    }
  ]);

  // 获取供应商列表（从搜索结果中提取）
  const suppliers = useMemo(() => {
    const supplierSet = new Set<string>();
    reagents.forEach(reagent => {
      if (reagent.supplier) {
        supplierSet.add(reagent.supplier);
      }
    });
    return Array.from(supplierSet).sort();
  }, [reagents]);

  // 处理搜索建议选择
  const handleSuggestionSelect = (value: string, suggestion: any) => {
    if (suggestion.type === 'supplier') {
      setSupplier(value);
    } else if (suggestion.type === 'category') {
      // 根据分类名称找到对应的枚举值
      const categoryEntry = Object.entries(CATEGORY_LABELS).find(
        ([_, label]) => label === value
      );
      if (categoryEntry) {
        setCategory(categoryEntry[0] as any);
      }
    } else {
      setQuery(value);
    }
  };

  // 生成搜索建议
  const searchSuggestions = useMemo(() => {
    const suggestions: any[] = [];

    // 添加热门关键词
    popularKeywords.forEach(keyword => {
      suggestions.push({
        value: keyword,
        label: keyword,
        type: 'reagent'
      });
    });

    // 添加供应商建议
    suppliers.slice(0, 5).forEach(supplier => {
      suggestions.push({
        value: supplier,
        label: supplier,
        type: 'supplier'
      });
    });

    return suggestions;
  }, [popularKeywords, suppliers]);

  // 渲染搜索结果
  const renderSearchResults = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center py-12">
          <Spin size="large" tip="搜索中..." />
        </div>
      );
    }

    if (error) {
      return (
        <Alert
          message="搜索失败"
          description={error}
          type="error"
          showIcon
          className="mb-4"
        />
      );
    }

    if (reagents.length === 0) {
      return (
        <Empty
          description="未找到匹配的试剂"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          className="py-12"
        >
          <Button type="primary" onClick={resetFilters}>
            清除筛选条件
          </Button>
        </Empty>
      );
    }

    const gridCols = {
      xs: 1,
      sm: 2,
      md: 2,
      lg: 3,
      xl: 4,
      xxl: 5
    };

    const listCols = {
      xs: 1,
      sm: 1,
      md: 1,
      lg: 1,
      xl: 1,
      xxl: 1
    };

    return (
      <Row gutter={[16, 16]}>
        {reagents.map(reagent => (
          <Col key={reagent.id} {...(viewMode === 'grid' ? gridCols : listCols)}>
            <ReagentCard
              reagent={reagent}
              onView={onReagentView}
              onEdit={onReagentEdit}
              size={viewMode === 'list' ? 'small' : 'default'}
            />
          </Col>
        ))}
      </Row>
    );
  };

  return (
    <div className={`reagent-search ${className}`}>
      <Layout className="bg-white">
        {/* 桌面端侧边栏筛选器 */}
        <Sider
          width={280}
          className="hidden lg:block bg-gray-50 border-r"
          theme="light"
        >
          <div className="p-4">
            <FilterPanel
              category={searchState.category}
              supplier={searchState.supplier}
              lowStock={searchState.lowStock}
              categoryStats={categoryStats}
              suppliers={suppliers}
              onCategoryChange={setCategory}
              onSupplierChange={setSupplier}
              onLowStockChange={setLowStock}
              onReset={resetFilters}
            />
          </div>
        </Sider>

        <Content className="min-h-screen">
          <div className="p-4 lg:p-6">
            {/* 搜索头部 */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-4">
                <Title level={2} className="mb-0">
                  试剂搜索
                </Title>
                <Tooltip
                  title={
                    <div className="space-y-1">
                      <div className="font-medium mb-2">键盘快捷键:</div>
                      {shortcuts.map((shortcut, index) => (
                        <div key={index} className="flex justify-between">
                          <span>{shortcut.description}</span>
                          <code className="ml-2 px-1 bg-gray-700 rounded text-xs">
                            {formatShortcut(shortcut)}
                          </code>
                        </div>
                      ))}
                    </div>
                  }
                  placement="bottomRight"
                >
                  <Button
                    type="text"
                    icon={<QuestionCircleOutlined />}
                    size="small"
                  >
                    快捷键
                  </Button>
                </Tooltip>
              </div>

              {/* 搜索框 */}
              <div ref={searchInputRef}>
                <SearchInput
                  value={searchState.query}
                  onSearch={setQuery}
                  onSuggestionSelect={handleSuggestionSelect}
                  suggestions={searchSuggestions}
                  loading={loading}
                  className="mb-4"
                />
              </div>

              {/* 移动端筛选器按钮 */}
              <div className="lg:hidden mb-4">
                <FilterPanel
                  category={searchState.category}
                  supplier={searchState.supplier}
                  lowStock={searchState.lowStock}
                  categoryStats={categoryStats}
                  suppliers={suppliers}
                  onCategoryChange={setCategory}
                  onSupplierChange={setSupplier}
                  onLowStockChange={setLowStock}
                  onReset={resetFilters}
                  collapsed
                />
              </div>
            </div>

            {/* 工具栏 */}
            <div className="flex justify-between items-center mb-4 flex-wrap gap-4">
              <div className="flex items-center space-x-4">
                <Text className="text-sm text-gray-600">
                  共找到 <span className="font-medium">{total}</span> 个试剂
                </Text>
              </div>

              <Space size="middle" wrap>
                {/* 视图切换 */}
                <Button.Group size="small">
                  <Button
                    type={viewMode === 'grid' ? 'primary' : 'default'}
                    icon={<AppstoreOutlined />}
                    onClick={() => setViewMode('grid')}
                  >
                    网格
                  </Button>
                  <Button
                    type={viewMode === 'list' ? 'primary' : 'default'}
                    icon={<BarsOutlined />}
                    onClick={() => setViewMode('list')}
                  >
                    列表
                  </Button>
                </Button.Group>

                {/* 排序控件 */}
                <SortControls
                  sortBy={searchState.sortBy}
                  sortOrder={searchState.sortOrder}
                  onSortChange={setSorting}
                />
              </Space>
            </div>

            {/* 搜索结果 */}
            {renderSearchResults()}

            {/* 分页 */}
            {total > 0 && (
              <div className="flex justify-center mt-8">
                <Pagination
                  current={searchState.currentPage}
                  total={total}
                  pageSize={searchState.pageSize}
                  onChange={setPage}
                  onShowSizeChange={(current, size) => setPageSize(size)}
                  showSizeChanger
                  showQuickJumper
                  showTotal={(total, range) =>
                    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                  }
                  pageSizeOptions={['10', '20', '50', '100']}
                />
              </div>
            )}
          </div>
        </Content>
      </Layout>

      {/* 移动端筛选器抽屉 */}
      <Drawer
        title="筛选器"
        placement="right"
        onClose={() => setFilterDrawerVisible(false)}
        open={filterDrawerVisible}
        width={320}
        className="lg:hidden"
      >
        <FilterPanel
          category={searchState.category}
          supplier={searchState.supplier}
          lowStock={searchState.lowStock}
          categoryStats={categoryStats}
          suppliers={suppliers}
          onCategoryChange={setCategory}
          onSupplierChange={setSupplier}
          onLowStockChange={setLowStock}
          onReset={resetFilters}
        />
      </Drawer>
    </div>
  );
};
