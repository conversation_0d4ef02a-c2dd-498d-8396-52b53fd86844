import { ReagentWithStatus } from '@haocai/shared/types/reagent';

// 临时禁用 tRPC，直到后端 API 完成
// TODO: 恢复 tRPC 配置当后端准备就绪

// Mock 试剂数据用于开发
const mockReagent: ReagentWithStatus = {
  id: '1',
  code: 'YF03.001',
  name: '亘诺细胞冻存液',
  specification: '100ml',
  category: 'BIOLOGICAL_REAGENT',
  supplier: '亘诺生物',
  manufacturer: '亘诺生物科技有限公司',
  casNumber: '12345-67-8',
  formula: 'C6H12O6',
  molecularWeight: '180.16',
  purity: '99.5%',
  storageCondition: '-20°C',
  safetyLevel: '低',
  currentStock: '25',
  minThreshold: '5',
  maxCapacity: '100',
  unit: '瓶',
  unitPrice: '150.00',
  totalPrice: '3750.00',
  location: 'A-01-01',
  description: '用于细胞冷冻保存的专用冻存液',
  isActive: true,
  createdAt: '2024-01-15T10:30:00Z',
  updatedAt: '2024-01-20T14:45:00Z',
  createdBy: 'admin',
  updatedBy: 'admin',
  stockStatus: 'IN_STOCK' as any,
  lastTransaction: {
    createdAt: '2024-01-20T14:45:00Z',
    type: '入库'
  }
};

// Mock tRPC 客户端，用于前端开发
export const trpc = {
  reagent: {
    search: {
      useQuery: () => ({
        data: undefined,
        isLoading: false,
        error: null,
        refetch: () => Promise.resolve()
      })
    },
    getById: {
      useQuery: (params?: any, options?: any) => ({
        data: mockReagent,
        isLoading: false,
        error: null,
        refetch: () => Promise.resolve()
      })
    }
  },
  withTRPC: (component: any) => component
};



// 临时类型定义，用于开发阶段
export type RouterInputs = any;
export type RouterOutputs = any;
