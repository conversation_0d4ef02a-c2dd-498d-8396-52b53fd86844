import { useEffect, useCallback } from 'react';

/**
 * 键盘快捷键配置接口
 */
interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  metaKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  callback: () => void;
  description: string;
}

/**
 * 键盘快捷键 Hook
 * 提供全局键盘快捷键支持
 */
export const useKeyboardShortcuts = (shortcuts: KeyboardShortcut[]) => {
  // 处理键盘事件
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // 如果焦点在输入框中，跳过某些快捷键
    const activeElement = document.activeElement;
    const isInputFocused = activeElement && (
      activeElement.tagName === 'INPUT' ||
      activeElement.tagName === 'TEXTAREA' ||
      activeElement.getAttribute('contenteditable') === 'true'
    );

    for (const shortcut of shortcuts) {
      const {
        key,
        ctrlKey = false,
        metaKey = false,
        shiftKey = false,
        altKey = false,
        callback
      } = shortcut;

      // 检查按键匹配
      const keyMatches = event.key.toLowerCase() === key.toLowerCase();
      const ctrlMatches = event.ctrlKey === ctrlKey;
      const metaMatches = event.metaKey === metaKey;
      const shiftMatches = event.shiftKey === shiftKey;
      const altMatches = event.altKey === altKey;

      if (keyMatches && ctrlMatches && metaMatches && shiftMatches && altMatches) {
        // 对于搜索快捷键，即使在输入框中也要响应
        if (key.toLowerCase() === 'k' && (ctrlKey || metaKey)) {
          event.preventDefault();
          callback();
          return;
        }

        // 其他快捷键在输入框中时跳过
        if (isInputFocused) {
          continue;
        }

        event.preventDefault();
        callback();
        return;
      }
    }
  }, [shortcuts]);

  // 注册和清理事件监听器
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  // 返回快捷键列表（用于显示帮助）
  return shortcuts;
};

/**
 * 格式化快捷键显示
 */
export const formatShortcut = (shortcut: KeyboardShortcut): string => {
  const parts: string[] = [];
  
  if (shortcut.ctrlKey) parts.push('Ctrl');
  if (shortcut.metaKey) parts.push('Cmd');
  if (shortcut.shiftKey) parts.push('Shift');
  if (shortcut.altKey) parts.push('Alt');
  
  parts.push(shortcut.key.toUpperCase());
  
  return parts.join(' + ');
};
