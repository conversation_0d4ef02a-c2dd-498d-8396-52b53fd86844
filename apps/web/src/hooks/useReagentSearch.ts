import { useState, useCallback, useMemo } from 'react';
import { trpc } from '@/utils/trpc';
import {
  SearchParams,
  ReagentWithStatus,
  SearchResult
} from '@haocai/shared/types/reagent';
import { useReagentStore } from '@/stores/reagentStore';

/**
 * 搜索状态接口
 */
interface SearchState {
  query: string;
  category?: string;
  supplier?: string;
  lowStock: boolean;
  currentPage: number;
  pageSize: number;
  sortBy: 'name' | 'code' | 'currentStock' | 'createdAt';
  sortOrder: 'asc' | 'desc';
}

/**
 * 搜索结果 Hook 返回值
 */
interface UseReagentSearchReturn {
  // 搜索状态
  searchState: SearchState;

  // 搜索结果
  reagents: ReagentWithStatus[];
  total: number;
  loading: boolean;
  error: string | null;

  // 筛选器数据
  categoryStats: Record<string, number>;
  popularKeywords: string[];

  // 操作函数
  setQuery: (query: string) => void;
  setCategory: (category?: string) => void;
  setSupplier: (supplier?: string) => void;
  setLowStock: (lowStock: boolean) => void;
  setPage: (page: number) => void;
  setPageSize: (pageSize: number) => void;
  setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  resetFilters: () => void;

  // 分页信息
  hasMore: boolean;
  totalPages: number;
}

/**
 * 试剂搜索 Hook
 * 管理搜索状态、API 调用和结果处理
 */
export const useReagentSearch = (initialFilters?: Partial<SearchState>): UseReagentSearchReturn => {
  // 获取搜索历史管理函数
  const { addSearchHistory } = useReagentStore();

  // 初始搜索状态
  const [searchState, setSearchState] = useState<SearchState>({
    query: '',
    category: undefined,
    supplier: undefined,
    lowStock: false,
    currentPage: 1,
    pageSize: 20,
    sortBy: 'createdAt',
    sortOrder: 'desc',
    ...initialFilters
  });

  // 构建搜索参数
  const searchParams = useMemo(() => ({
    query: searchState.query || undefined,
    category: searchState.category as any,
    supplier: searchState.supplier,
    lowStock: searchState.lowStock || undefined,
    limit: searchState.pageSize,
    offset: (searchState.currentPage - 1) * searchState.pageSize,
    sortBy: searchState.sortBy,
    sortOrder: searchState.sortOrder
  }), [searchState]);

  // tRPC 查询 - 暂时使用 mock 数据，等待后端 API 完成
  // TODO: 恢复真实的 tRPC 调用当后端 API 准备就绪
  const searchResult: SearchResult | undefined = {
    reagents: [],
    pagination: {
      total: 0,
      limit: searchParams.limit || 20,
      offset: searchParams.offset || 0,
      hasMore: false
    },
    filters: {
      categories: {},
      popularKeywords: []
    }
  };
  const isLoading = false;
  const queryError = null;
  const refetch = () => Promise.resolve();

  // 解析搜索结果
  const reagents = searchResult?.reagents || [];
  const total = searchResult?.pagination.total || 0;
  const hasMore = searchResult?.pagination.hasMore || false;
  const categoryStats = searchResult?.filters.categories || {} as Record<string, number>;
  const popularKeywords = searchResult?.filters.popularKeywords || [];

  // 计算总页数
  const totalPages = Math.ceil(total / searchState.pageSize);

  // 错误处理
  const error = queryError ? String(queryError) : null;

  // 更新查询关键词
  const setQuery = useCallback((query: string) => {
    setSearchState(prev => ({
      ...prev,
      query,
      currentPage: 1 // 重置到第一页
    }));

    // 如果有查询内容，添加到搜索历史（在搜索结果返回后更新结果数量）
    if (query.trim()) {
      addSearchHistory(query.trim(), total);
    }
  }, [addSearchHistory, total]);

  // 更新分类筛选
  const setCategory = useCallback((category?: string) => {
    setSearchState(prev => ({
      ...prev,
      category,
      currentPage: 1
    }));
  }, []);

  // 更新供应商筛选
  const setSupplier = useCallback((supplier?: string) => {
    setSearchState(prev => ({
      ...prev,
      supplier,
      currentPage: 1
    }));
  }, []);

  // 更新低库存筛选
  const setLowStock = useCallback((lowStock: boolean) => {
    setSearchState(prev => ({
      ...prev,
      lowStock,
      currentPage: 1
    }));
  }, []);

  // 更新页码
  const setPage = useCallback((currentPage: number) => {
    setSearchState(prev => ({
      ...prev,
      currentPage
    }));
  }, []);

  // 更新页面大小
  const setPageSize = useCallback((pageSize: number) => {
    setSearchState(prev => ({
      ...prev,
      pageSize,
      currentPage: 1
    }));
  }, []);

  // 更新排序
  const setSorting = useCallback((sortBy: string, sortOrder: 'asc' | 'desc') => {
    setSearchState(prev => ({
      ...prev,
      sortBy: sortBy as 'name' | 'code' | 'currentStock' | 'createdAt',
      sortOrder,
      currentPage: 1
    }));
  }, []);

  // 重置筛选器
  const resetFilters = useCallback(() => {
    setSearchState(prev => ({
      ...prev,
      category: undefined,
      supplier: undefined,
      lowStock: false,
      currentPage: 1
    }));
  }, []);

  return {
    // 搜索状态
    searchState,

    // 搜索结果
    reagents,
    total,
    loading: isLoading,
    error,

    // 筛选器数据
    categoryStats,
    popularKeywords,

    // 操作函数
    setQuery,
    setCategory,
    setSupplier,
    setLowStock,
    setPage,
    setPageSize,
    setSorting,
    resetFilters,

    // 分页信息
    hasMore,
    totalPages
  };
};
