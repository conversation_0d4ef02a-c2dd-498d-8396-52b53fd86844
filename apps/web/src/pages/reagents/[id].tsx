import React from 'react';
import { useRouter } from 'next/router';
import { Layout, Spin, Alert, Button } from 'antd';
import { ArrowLeftOutlined, EditOutlined } from '@ant-design/icons';
import { trpc } from '@/utils/trpc';
import {
  ReagentDetailCard,
  StockInfoCard,
  ReagentPropertiesCard
} from '@/components/features/ReagentDetail';

const { Content } = Layout;

/**
 * 试剂详情页面
 * 展示试剂的完整信息，包括基本信息、库存状态和详细属性
 */
export default function ReagentDetailPage() {
  const router = useRouter();
  const { id } = router.query;

  // 获取试剂详情数据
  const {
    data: reagent,
    isLoading,
    error,
    refetch
  } = trpc.reagent.getById.useQuery(
    { id: id as string },
    {
      enabled: !!id && typeof id === 'string',
      retry: 1,
      refetchOnWindowFocus: false,
    }
  );

  // 处理返回操作
  const handleGoBack = () => {
    router.back();
  };

  // 处理编辑操作
  const handleEdit = () => {
    if (reagent?.id) {
      router.push(`/reagents/edit/${reagent.id}`);
    }
  };

  // 加载状态
  if (isLoading) {
    return (
      <Layout className="min-h-screen bg-gray-50">
        <Content className="p-6">
          <div className="flex justify-center items-center min-h-[400px]">
            <Spin size="large" tip="正在加载试剂详情..." />
          </div>
        </Content>
      </Layout>
    );
  }

  // 错误状态
  if (error) {
    return (
      <Layout className="min-h-screen bg-gray-50">
        <Content className="p-6">
          <div className="max-w-4xl mx-auto">
            <div className="mb-6">
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={handleGoBack}
                className="mb-4"
              >
                返回
              </Button>
            </div>
            <Alert
              message="加载失败"
              description={'无法加载试剂详情，请稍后重试'}
              type="error"
              showIcon
              action={
                <Button size="small" onClick={() => refetch()}>
                  重试
                </Button>
              }
            />
          </div>
        </Content>
      </Layout>
    );
  }

  // 试剂不存在
  if (!reagent) {
    return (
      <Layout className="min-h-screen bg-gray-50">
        <Content className="p-6">
          <div className="max-w-4xl mx-auto">
            <div className="mb-6">
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={handleGoBack}
                className="mb-4"
              >
                返回
              </Button>
            </div>
            <Alert
              message="试剂不存在"
              description="您访问的试剂可能已被删除或不存在"
              type="warning"
              showIcon
              action={
                <Button type="primary" onClick={() => router.push('/reagents')}>
                  返回试剂列表
                </Button>
              }
            />
          </div>
        </Content>
      </Layout>
    );
  }

  return (
    <Layout className="min-h-screen bg-gray-50">
      <Content className="p-4 md:p-6">
        <div className="max-w-6xl mx-auto">
          {/* 页面头部 - 导航和操作按钮 */}
          <div className="mb-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex items-center gap-4">
                <Button
                  icon={<ArrowLeftOutlined />}
                  onClick={handleGoBack}
                  className="flex-shrink-0"
                >
                  返回
                </Button>
                <div>
                  <h1 className="text-xl md:text-2xl font-semibold text-gray-900 mb-1">
                    {reagent.name}
                  </h1>
                  <p className="text-sm text-gray-500">
                    编码: {reagent.code}
                  </p>
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  type="primary"
                  icon={<EditOutlined />}
                  onClick={handleEdit}
                  className="flex-shrink-0"
                >
                  编辑试剂
                </Button>
              </div>
            </div>
          </div>

          {/* 主要内容区域 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 左侧 - 基本信息和库存信息 */}
            <div className="lg:col-span-2 space-y-6">
              {/* 试剂基本信息卡片 */}
              <ReagentDetailCard reagent={reagent} />

              {/* 库存信息卡片 */}
              <StockInfoCard reagent={reagent} />
            </div>

            {/* 右侧 - 详细属性 */}
            <div className="lg:col-span-1">
              <ReagentPropertiesCard reagent={reagent} />
            </div>
          </div>
        </div>
      </Content>
    </Layout>
  );
}
