import React, { useState } from 'react';
import { Layout, Modal, message } from 'antd';
import { ReagentSearch } from '@/components/features/ReagentSearch';
import { ReagentWithStatus } from '@haocai/shared/types/reagent';

const { Header, Content } = Layout;

/**
 * 试剂搜索页面
 */
export default function ReagentsPage() {
  // 试剂详情模态框状态
  const [selectedReagent, setSelectedReagent] = useState<ReagentWithStatus | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  // 处理查看试剂详情
  const handleReagentView = (reagent: ReagentWithStatus) => {
    setSelectedReagent(reagent);
    setDetailModalVisible(true);
  };

  // 处理编辑试剂
  const handleReagentEdit = (reagent: ReagentWithStatus) => {
    message.info(`编辑试剂: ${reagent.name} (功能待实现)`);
  };

  // 关闭详情模态框
  const handleDetailModalClose = () => {
    setDetailModalVisible(false);
    setSelectedReagent(null);
  };

  // 渲染试剂详情
  const renderReagentDetail = () => {
    if (!selectedReagent) return null;

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">基本信息</h4>
            <div className="space-y-2 text-sm">
              <div><span className="text-gray-600">编码:</span> {selectedReagent.code}</div>
              <div><span className="text-gray-600">名称:</span> {selectedReagent.name}</div>
              <div><span className="text-gray-600">规格:</span> {selectedReagent.specification || '未指定'}</div>
              <div><span className="text-gray-600">供应商:</span> {selectedReagent.supplier || '未指定'}</div>
              <div><span className="text-gray-600">生产厂家:</span> {selectedReagent.manufacturer || '未指定'}</div>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 mb-2">库存信息</h4>
            <div className="space-y-2 text-sm">
              <div><span className="text-gray-600">当前库存:</span> {selectedReagent.currentStock} {selectedReagent.unit}</div>
              <div><span className="text-gray-600">警戒线:</span> {selectedReagent.minThreshold} {selectedReagent.unit}</div>
              <div><span className="text-gray-600">最大容量:</span> {selectedReagent.maxCapacity || '未设置'} {selectedReagent.unit}</div>
              <div><span className="text-gray-600">存储位置:</span> {selectedReagent.location || '未指定'}</div>
              <div><span className="text-gray-600">存储条件:</span> {selectedReagent.storageCondition || '未指定'}</div>
            </div>
          </div>
        </div>

        {selectedReagent.description && (
          <div>
            <h4 className="font-medium text-gray-900 mb-2">描述</h4>
            <p className="text-sm text-gray-600">{selectedReagent.description}</p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">技术参数</h4>
            <div className="space-y-2 text-sm">
              {selectedReagent.casNumber && (
                <div><span className="text-gray-600">CAS号:</span> {selectedReagent.casNumber}</div>
              )}
              {selectedReagent.formula && (
                <div><span className="text-gray-600">分子式:</span> {selectedReagent.formula}</div>
              )}
              {selectedReagent.molecularWeight && (
                <div><span className="text-gray-600">分子量:</span> {selectedReagent.molecularWeight}</div>
              )}
              {selectedReagent.purity && (
                <div><span className="text-gray-600">纯度:</span> {selectedReagent.purity}</div>
              )}
              {selectedReagent.safetyLevel && (
                <div><span className="text-gray-600">安全等级:</span> {selectedReagent.safetyLevel}</div>
              )}
            </div>
          </div>

          <div>
            <h4 className="font-medium text-gray-900 mb-2">价格信息</h4>
            <div className="space-y-2 text-sm">
              {selectedReagent.unitPrice && (
                <div><span className="text-gray-600">单价:</span> ¥{Number(selectedReagent.unitPrice).toFixed(2)}</div>
              )}
              {selectedReagent.totalPrice && (
                <div><span className="text-gray-600">总价:</span> ¥{Number(selectedReagent.totalPrice).toFixed(2)}</div>
              )}
            </div>
          </div>
        </div>

        <div>
          <h4 className="font-medium text-gray-900 mb-2">记录信息</h4>
          <div className="space-y-2 text-sm">
            <div><span className="text-gray-600">创建时间:</span> {new Date(selectedReagent.createdAt).toLocaleString()}</div>
            <div><span className="text-gray-600">更新时间:</span> {new Date(selectedReagent.updatedAt).toLocaleString()}</div>
            <div><span className="text-gray-600">状态:</span> {selectedReagent.isActive ? '激活' : '停用'}</div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Layout className="min-h-screen bg-gray-50">
      <Content>
        <ReagentSearch
          onReagentView={handleReagentView}
          onReagentEdit={handleReagentEdit}
        />
      </Content>

      {/* 试剂详情模态框 */}
      <Modal
        title={selectedReagent ? `试剂详情 - ${selectedReagent.name}` : '试剂详情'}
        open={detailModalVisible}
        onCancel={handleDetailModalClose}
        footer={null}
        width={800}
        className="reagent-detail-modal"
      >
        {renderReagentDetail()}
      </Modal>
    </Layout>
  );
}
