import { Layout, Card, Statistic, Row, Col, Typography } from 'antd';
import { 
  ExperimentOutlined, 
  AlertOutlined, 
  TruckOutlined, 
  UserOutlined 
} from '@ant-design/icons';

const { Header, Content } = Layout;
const { Title } = Typography;

export default function Dashboard() {
  return (
    <Layout className="min-h-screen">
      <Header className="bg-white shadow-sm">
        <div className="flex items-center justify-between">
          <Title level={3} className="m-0">
            试剂库存管理系统
          </Title>
          <div className="text-gray-600">
            欢迎使用
          </div>
        </div>
      </Header>
      
      <Content className="p-6">
        <div className="mb-6">
          <Title level={2}>仪表板</Title>
          <p className="text-gray-600">系统概览和关键指标</p>
        </div>

        <Row gutter={[16, 16]} className="mb-6">
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="试剂总数"
                value={0}
                prefix={<ExperimentOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="库存警报"
                value={0}
                prefix={<AlertOutlined />}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="今日交易"
                value={0}
                prefix={<TruckOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="活跃用户"
                value={0}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="最近交易" className="h-96">
              <div className="flex items-center justify-center h-64 text-gray-500">
                暂无数据
              </div>
            </Card>
          </Col>
          
          <Col xs={24} lg={12}>
            <Card title="库存警报" className="h-96">
              <div className="flex items-center justify-center h-64 text-gray-500">
                暂无警报
              </div>
            </Card>
          </Col>
        </Row>
      </Content>
    </Layout>
  );
}
