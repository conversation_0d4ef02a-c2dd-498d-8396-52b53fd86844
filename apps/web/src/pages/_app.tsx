import type { AppProps } from 'next/app';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { trpc } from '@/utils/trpc';
import '@/styles/globals.css';

function App({ Component, pageProps }: AppProps) {
  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 6,
        },
      }}
    >
      <Component {...pageProps} />
    </ConfigProvider>
  );
}

export default trpc.withTRPC(App);
