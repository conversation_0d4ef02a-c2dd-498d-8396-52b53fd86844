import { create } from 'zustand';
import { persist } from 'zustand/middleware';

/**
 * 搜索历史记录项
 */
interface SearchHistoryItem {
  id: string;
  query: string;
  timestamp: number;
  resultCount?: number;
}

/**
 * 试剂搜索状态接口
 */
interface ReagentSearchState {
  // 搜索历史
  searchHistory: SearchHistoryItem[];
  
  // 操作函数
  addSearchHistory: (query: string, resultCount?: number) => void;
  removeSearchHistory: (id: string) => void;
  clearSearchHistory: () => void;
  getRecentSearches: (limit?: number) => SearchHistoryItem[];
}

/**
 * 试剂搜索状态管理
 * 使用 Zustand 进行客户端状态管理
 */
export const useReagentStore = create<ReagentSearchState>()(
  persist(
    (set, get) => ({
      // 初始状态
      searchHistory: [],

      // 添加搜索历史
      addSearchHistory: (query: string, resultCount?: number) => {
        const trimmedQuery = query.trim();
        if (!trimmedQuery) return;

        set((state) => {
          // 检查是否已存在相同的搜索
          const existingIndex = state.searchHistory.findIndex(
            item => item.query.toLowerCase() === trimmedQuery.toLowerCase()
          );

          let newHistory = [...state.searchHistory];

          if (existingIndex >= 0) {
            // 如果存在，更新时间戳并移到最前面
            const existingItem = newHistory[existingIndex];
            newHistory.splice(existingIndex, 1);
            newHistory.unshift({
              ...existingItem,
              timestamp: Date.now(),
              resultCount
            });
          } else {
            // 如果不存在，添加新记录
            const newItem: SearchHistoryItem = {
              id: `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              query: trimmedQuery,
              timestamp: Date.now(),
              resultCount
            };
            newHistory.unshift(newItem);
          }

          // 限制历史记录数量（最多保留50条）
          if (newHistory.length > 50) {
            newHistory = newHistory.slice(0, 50);
          }

          return { searchHistory: newHistory };
        });
      },

      // 删除搜索历史
      removeSearchHistory: (id: string) => {
        set((state) => ({
          searchHistory: state.searchHistory.filter(item => item.id !== id)
        }));
      },

      // 清空搜索历史
      clearSearchHistory: () => {
        set({ searchHistory: [] });
      },

      // 获取最近的搜索记录
      getRecentSearches: (limit = 10) => {
        const { searchHistory } = get();
        return searchHistory
          .sort((a, b) => b.timestamp - a.timestamp)
          .slice(0, limit);
      }
    }),
    {
      name: 'reagent-search-storage', // 本地存储键名
      partialize: (state) => ({ 
        searchHistory: state.searchHistory 
      }), // 只持久化搜索历史
    }
  )
);
