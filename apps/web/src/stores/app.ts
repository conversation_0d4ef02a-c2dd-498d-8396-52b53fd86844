import { create } from 'zustand';

interface AppState {
  // UI状态
  sidebarCollapsed: boolean;
  theme: 'light' | 'dark';
  loading: boolean;
  
  // 通知状态
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message: string;
    timestamp: Date;
  }>;
  
  // 页面状态
  currentPage: string;
  breadcrumbs: Array<{
    title: string;
    path?: string;
  }>;
  
  // Actions
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  setTheme: (theme: 'light' | 'dark') => void;
  setLoading: (loading: boolean) => void;
  addNotification: (notification: Omit<AppState['notifications'][0], 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  setCurrentPage: (page: string) => void;
  setBreadcrumbs: (breadcrumbs: AppState['breadcrumbs']) => void;
}

export const useAppStore = create<AppState>((set, get) => ({
  // 初始状态
  sidebarCollapsed: false,
  theme: 'light',
  loading: false,
  notifications: [],
  currentPage: '',
  breadcrumbs: [],

  // Actions
  toggleSidebar: () => {
    set((state) => ({
      sidebarCollapsed: !state.sidebarCollapsed,
    }));
  },

  setSidebarCollapsed: (collapsed: boolean) => {
    set({ sidebarCollapsed: collapsed });
  },

  setTheme: (theme: 'light' | 'dark') => {
    set({ theme });
    // 这里可以添加主题切换逻辑
    document.documentElement.setAttribute('data-theme', theme);
  },

  setLoading: (loading: boolean) => {
    set({ loading });
  },

  addNotification: (notification) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newNotification = {
      ...notification,
      id,
      timestamp: new Date(),
    };
    
    set((state) => ({
      notifications: [newNotification, ...state.notifications],
    }));

    // 自动移除通知（5秒后）
    setTimeout(() => {
      get().removeNotification(id);
    }, 5000);
  },

  removeNotification: (id: string) => {
    set((state) => ({
      notifications: state.notifications.filter((n) => n.id !== id),
    }));
  },

  clearNotifications: () => {
    set({ notifications: [] });
  },

  setCurrentPage: (page: string) => {
    set({ currentPage: page });
  },

  setBreadcrumbs: (breadcrumbs) => {
    set({ breadcrumbs });
  },
}));
