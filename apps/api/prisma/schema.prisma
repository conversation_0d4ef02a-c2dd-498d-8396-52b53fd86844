// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id        String   @id @default(uuid())
  email     String   @unique
  username  String   @unique
  password  String
  name      String
  role      UserRole @default(VIEWER)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  stockTransactions StockTransaction[]
  stockAlerts       StockAlert[]
  createdReagents   Reagent[] @relation("ReagentCreator")
  updatedReagents   Reagent[] @relation("ReagentUpdater")

  @@map("users")
}

// 用户角色枚举
enum UserRole {
  ADMIN
  MANAGER
  OPERATOR
  VIEWER
}

// 试剂表
model Reagent {
  id           String  @id @default(uuid())
  code         String  @unique // 试剂编码 (YF03/YF04/YF06)
  name         String  // 试剂名称
  specification String? // 规格/体积规格
  category     ReagentCategory // 分类
  supplier     String? // 供应商
  manufacturer String? // 生产厂家
  casNumber    String? // CAS号
  formula      String? // 分子式
  molecularWeight Decimal? @db.Decimal(10,3) // 分子量
  purity       String? // 纯度
  storageCondition String? // 储存条件
  safetyLevel  String? // 安全等级
  currentStock Decimal @default(0) @db.Decimal(10,3) // 当前库存数量
  minThreshold Decimal @default(0) @db.Decimal(10,3) // 最小库存警戒线
  maxCapacity  Decimal? @db.Decimal(10,3) // 最大存储容量
  unit         String  @default("瓶") // 计量单位
  unitPrice    Decimal? @db.Decimal(10,2) // 单价
  totalPrice   Decimal? @db.Decimal(10,2) // 总价
  location     String? // 物理存储位置
  description  String? // 描述
  isActive     Boolean @default(true) // 是否当前使用中
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  createdBy    String? // 创建人
  updatedBy    String? // 更新人

  // 关联关系
  stockTransactions StockTransaction[]
  stockAlerts       StockAlert[]
  creator           User? @relation("ReagentCreator", fields: [createdBy], references: [id])
  updater           User? @relation("ReagentUpdater", fields: [updatedBy], references: [id])

  // 索引
  @@index([code], name: "idx_reagents_code")
  @@index([name], name: "idx_reagents_name")
  @@index([category], name: "idx_reagents_category")
  @@index([supplier], name: "idx_reagents_supplier")
  @@index([currentStock], name: "idx_reagents_current_stock")
  @@index([isActive], name: "idx_reagents_is_active")
  @@index([currentStock, minThreshold], name: "idx_reagents_stock_status")
  @@map("reagents")
}

// 试剂分类枚举
enum ReagentCategory {
  BIOLOGICAL_REAGENT // YF03 - 生物试剂
  LAB_CONSUMABLE     // YF04 - 实验室耗材
  CULTURE_MEDIUM     // YF06 - 培养基
}

// 库存交易记录表
model StockTransaction {
  id          String            @id @default(uuid())
  reagentId   String
  userId      String
  type        TransactionType   // 交易类型
  quantity    Decimal           @db.Decimal(10,3) // 数量变化
  beforeStock Decimal           @db.Decimal(10,3) // 变化前库存
  afterStock  Decimal           @db.Decimal(10,3) // 变化后库存
  reason      String?           // 变化原因
  batchNumber String?           // 批次号
  expiryDate  DateTime?         // 过期日期
  supplier    String?           // 供应商
  price       Decimal?          @db.Decimal(10,2) // 单价
  totalPrice  Decimal?          @db.Decimal(10,2) // 总价
  invoiceNumber String?         // 发票号
  notes       String?           // 备注
  createdAt   DateTime          @default(now())

  // 关联关系
  reagent Reagent @relation(fields: [reagentId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id])

  @@map("stock_transactions")
}

// 交易类型枚举
enum TransactionType {
  IN      // 入库
  OUT     // 出库
  ADJUST  // 调整
  RETURN  // 退货
  LOSS    // 损耗
}

// 库存警报表
model StockAlert {
  id        String      @id @default(uuid())
  reagentId String
  userId    String?     // 处理人员
  type      AlertType   // 警报类型
  level     AlertLevel  // 警报级别
  message   String      // 警报消息
  isRead    Boolean     @default(false) // 是否已读
  isResolved Boolean    @default(false) // 是否已解决
  resolvedAt DateTime?  // 解决时间
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt

  // 关联关系
  reagent Reagent @relation(fields: [reagentId], references: [id], onDelete: Cascade)
  user    User?   @relation(fields: [userId], references: [id])

  @@map("stock_alerts")
}

// 警报类型枚举
enum AlertType {
  LOW_STOCK    // 库存不足
  EXPIRY_SOON  // 即将过期
  EXPIRED      // 已过期
  OUT_OF_STOCK // 缺货
}

// 警报级别枚举
enum AlertLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}
