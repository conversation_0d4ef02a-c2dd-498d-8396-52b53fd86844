import { PrismaClient, ReagentCategory, UserRole } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('开始种子数据初始化...');

  // 创建测试用户
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'admin',
      password: 'hashed_password_here', // 实际应用中应该是哈希后的密码
      name: '系统管理员',
      role: UserRole.ADMIN,
      isActive: true,
    },
  });

  const operatorUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'operator',
      password: 'hashed_password_here',
      name: '实验室操作员',
      role: UserRole.OPERATOR,
      isActive: true,
    },
  });

  console.log('用户创建完成');

  // 创建生物试剂 (YF03)
  const biologicalReagents = [
    {
      code: 'YF03-001',
      name: '亘诺细胞冻存液',
      specification: '50ml',
      category: ReagentCategory.BIOLOGICAL_REAGENT,
      supplier: '亘诺生物科技',
      manufacturer: '亘诺生物科技有限公司',
      casNumber: 'N/A',
      storageCondition: '-80°C',
      safetyLevel: 'P2',
      currentStock: 25.5,
      minThreshold: 10.0,
      maxCapacity: 100.0,
      unit: '瓶',
      unitPrice: 280.00,
      location: 'A区-冷冻柜-1',
      description: '用于细胞长期保存的专用冻存液',
      createdBy: adminUser.id,
    },
    {
      code: 'YF03-002',
      name: 'DMEM培养基',
      specification: '500ml',
      category: ReagentCategory.BIOLOGICAL_REAGENT,
      supplier: 'Gibco',
      manufacturer: 'Thermo Fisher Scientific',
      casNumber: 'N/A',
      storageCondition: '4°C',
      safetyLevel: 'P1',
      currentStock: 15.0,
      minThreshold: 5.0,
      maxCapacity: 50.0,
      unit: '瓶',
      unitPrice: 320.00,
      location: 'B区-冷藏柜-2',
      description: '细胞培养用基础培养基',
      createdBy: adminUser.id,
    },
  ];

  // 创建实验室耗材 (YF04)
  const labConsumables = [
    {
      code: 'YF04-001',
      name: '无菌移液器吸头',
      specification: '1000μl',
      category: ReagentCategory.LAB_CONSUMABLE,
      supplier: 'Eppendorf',
      manufacturer: 'Eppendorf AG',
      casNumber: 'N/A',
      storageCondition: '室温',
      safetyLevel: 'P1',
      currentStock: 500.0,
      minThreshold: 100.0,
      maxCapacity: 2000.0,
      unit: '盒',
      unitPrice: 45.00,
      location: 'C区-储物柜-3',
      description: '无菌包装的移液器吸头',
      createdBy: operatorUser.id,
    },
    {
      code: 'YF04-002',
      name: '细胞培养皿',
      specification: '90mm',
      category: ReagentCategory.LAB_CONSUMABLE,
      supplier: 'Corning',
      manufacturer: 'Corning Incorporated',
      casNumber: 'N/A',
      storageCondition: '室温',
      safetyLevel: 'P1',
      currentStock: 200.0,
      minThreshold: 50.0,
      maxCapacity: 1000.0,
      unit: '包',
      unitPrice: 85.00,
      location: 'C区-储物柜-1',
      description: '细胞培养专用培养皿',
      createdBy: operatorUser.id,
    },
  ];

  // 创建培养基 (YF06)
  const cultureMedia = [
    {
      code: 'YF06-001',
      name: 'LB培养基',
      specification: '1L',
      category: ReagentCategory.CULTURE_MEDIUM,
      supplier: '生工生物',
      manufacturer: '生工生物工程股份有限公司',
      casNumber: 'N/A',
      storageCondition: '4°C',
      safetyLevel: 'P1',
      currentStock: 8.0,
      minThreshold: 3.0,
      maxCapacity: 30.0,
      unit: '瓶',
      unitPrice: 120.00,
      location: 'B区-冷藏柜-1',
      description: '细菌培养用LB培养基',
      createdBy: adminUser.id,
    },
    {
      code: 'YF06-002',
      name: 'YPD培养基',
      specification: '500ml',
      category: ReagentCategory.CULTURE_MEDIUM,
      supplier: 'BD Biosciences',
      manufacturer: 'Becton Dickinson',
      casNumber: 'N/A',
      storageCondition: '4°C',
      safetyLevel: 'P1',
      currentStock: 12.0,
      minThreshold: 5.0,
      maxCapacity: 40.0,
      unit: '瓶',
      unitPrice: 180.00,
      location: 'B区-冷藏柜-3',
      description: '酵母培养用YPD培养基',
      createdBy: adminUser.id,
    },
  ];

  // 批量创建试剂
  const allReagents = [...biologicalReagents, ...labConsumables, ...cultureMedia];

  for (const reagentData of allReagents) {
    await prisma.reagent.upsert({
      where: { code: reagentData.code },
      update: {},
      create: reagentData,
    });
  }

  console.log('试剂数据创建完成');
  console.log(`共创建 ${allReagents.length} 个试剂记录`);
  console.log('种子数据初始化完成！');
}

main()
  .catch((e) => {
    console.error('种子数据初始化失败:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
