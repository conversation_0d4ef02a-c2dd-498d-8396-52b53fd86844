{"name": "@haocai/api", "version": "1.0.0", "description": "试剂库存管理系统 - 后端API", "private": true, "main": "dist/server.js", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit", "test": "jest", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "clean": "rm -rf dist", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"@prisma/client": "^5.7.0", "@trpc/server": "^10.45.0", "@types/lodash-es": "^4.17.12", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.0", "express": "^4.18.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.0", "lodash-es": "^4.17.21", "morgan": "^1.10.0", "redis": "^4.6.0", "zod": "^3.22.0"}, "devDependencies": {"@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.0", "@types/cors": "^2.8.0", "@types/express": "^4.17.0", "@types/jest": "^29.5.0", "@types/jsonwebtoken": "^9.0.0", "@types/morgan": "^1.9.0", "@types/node": "^20.10.0", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "jest": "^29.7.0", "jest-mock-extended": "^4.0.0", "prisma": "^5.7.0", "supertest": "^6.3.0", "ts-jest": "^29.1.0", "tsx": "^4.6.0", "typescript": "^5.3.0"}}