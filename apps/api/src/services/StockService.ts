import { PrismaClient, TransactionType } from '@prisma/client';

/**
 * 库存操作输入接口
 */
export interface StockInInput {
  reagentId: string;
  quantity: number;
  reason?: string;
  batchNumber?: string;
  expiryDate?: Date;
  supplier?: string;
  price?: number;
  invoiceNumber?: string;
  notes?: string;
}

export interface StockOutInput {
  reagentId: string;
  quantity: number;
  reason?: string;
  projectCode?: string;
  notes?: string;
}

export interface StockAdjustInput {
  reagentId: string;
  newQuantity: number;
  reason: string;
  notes?: string;
}

export interface BatchStockInInput {
  items: StockInInput[];
}

/**
 * 库存操作结果接口
 */
export interface StockOperationResult {
  transactionId: string;
  reagentId: string;
  type: TransactionType;
  quantity: number;
  beforeStock: number;
  afterStock: number;
  success: boolean;
  message?: string;
}

/**
 * 库存业务逻辑服务
 * 处理所有库存相关的业务操作，包括入库、出库、调整等
 */
export class StockService {
  constructor(private prisma: PrismaClient) { }

  /**
   * 入库操作
   */
  async stockIn(input: StockInInput, userId: string): Promise<StockOperationResult> {
    return await this.prisma.$transaction(async (tx) => {
      // 验证试剂存在性
      const reagent = await this.validateReagentExists(tx, input.reagentId);

      const beforeStock = Number(reagent.currentStock);
      const afterStock = beforeStock + input.quantity;

      // 检查最大容量限制
      if (reagent.maxCapacity && afterStock > Number(reagent.maxCapacity)) {
        throw new Error(`入库后库存 ${afterStock} 将超过最大容量 ${reagent.maxCapacity}`);
      }

      // 更新库存
      await tx.reagent.update({
        where: { id: input.reagentId },
        data: { currentStock: afterStock },
      });

      // 创建交易记录
      const transaction = await tx.stockTransaction.create({
        data: {
          reagentId: input.reagentId,
          userId,
          type: 'IN',
          quantity: input.quantity,
          beforeStock,
          afterStock,
          reason: input.reason,
          batchNumber: input.batchNumber,
          expiryDate: input.expiryDate,
          supplier: input.supplier,
          price: input.price,
          totalPrice: input.price ? input.price * input.quantity : null,
          invoiceNumber: input.invoiceNumber,
          notes: input.notes,
        },
      });

      return {
        transactionId: transaction.id,
        reagentId: input.reagentId,
        type: 'IN',
        quantity: input.quantity,
        beforeStock,
        afterStock,
        success: true,
        message: `成功入库 ${input.quantity} ${reagent.unit}`,
      };
    });
  }

  /**
   * 出库操作
   */
  async stockOut(input: StockOutInput, userId: string): Promise<StockOperationResult> {
    return await this.prisma.$transaction(async (tx) => {
      // 验证试剂存在性
      const reagent = await this.validateReagentExists(tx, input.reagentId);

      const beforeStock = Number(reagent.currentStock);

      // 验证库存充足性
      if (beforeStock < input.quantity) {
        throw new Error(`库存不足，当前库存：${beforeStock}，需要：${input.quantity}`);
      }

      const afterStock = beforeStock - input.quantity;

      // 更新库存
      await tx.reagent.update({
        where: { id: input.reagentId },
        data: { currentStock: afterStock },
      });

      // 创建交易记录
      const transaction = await tx.stockTransaction.create({
        data: {
          reagentId: input.reagentId,
          userId,
          type: 'OUT',
          quantity: -input.quantity, // 负数表示出库
          beforeStock,
          afterStock,
          reason: input.reason,
          notes: input.notes,
        },
      });

      // 检查是否需要创建库存警报
      await this.checkAndCreateStockAlert(tx, reagent, afterStock);

      return {
        transactionId: transaction.id,
        reagentId: input.reagentId,
        type: 'OUT',
        quantity: input.quantity,
        beforeStock,
        afterStock,
        success: true,
        message: `成功出库 ${input.quantity} ${reagent.unit}`,
      };
    });
  }

  /**
   * 库存调整操作
   */
  async adjust(input: StockAdjustInput, userId: string): Promise<StockOperationResult> {
    return await this.prisma.$transaction(async (tx) => {
      // 验证试剂存在性
      const reagent = await this.validateReagentExists(tx, input.reagentId);

      const beforeStock = Number(reagent.currentStock);
      const afterStock = input.newQuantity;
      const quantity = afterStock - beforeStock;

      // 更新库存
      await tx.reagent.update({
        where: { id: input.reagentId },
        data: { currentStock: afterStock },
      });

      // 创建调整记录
      const transaction = await tx.stockTransaction.create({
        data: {
          reagentId: input.reagentId,
          userId,
          type: 'ADJUST',
          quantity,
          beforeStock,
          afterStock,
          reason: input.reason,
          notes: input.notes,
        },
      });

      return {
        transactionId: transaction.id,
        reagentId: input.reagentId,
        type: 'ADJUST',
        quantity: Math.abs(quantity),
        beforeStock,
        afterStock,
        success: true,
        message: `成功调整库存至 ${afterStock} ${reagent.unit}`,
      };
    });
  }

  /**
   * 批量入库操作
   */
  async batchStockIn(input: BatchStockInInput, userId: string): Promise<StockOperationResult[]> {
    if (input.items.length === 0) {
      throw new Error('批量操作项目不能为空');
    }

    if (input.items.length > 50) {
      throw new Error('批量操作项目数量不能超过50个');
    }

    return await this.prisma.$transaction(async (tx) => {
      const results: StockOperationResult[] = [];

      for (const item of input.items) {
        try {
          // 验证试剂存在性
          const reagent = await this.validateReagentExists(tx, item.reagentId);

          const beforeStock = Number(reagent.currentStock);
          const afterStock = beforeStock + item.quantity;

          // 检查最大容量限制
          if (reagent.maxCapacity && afterStock > Number(reagent.maxCapacity)) {
            throw new Error(`入库后库存 ${afterStock} 将超过最大容量 ${reagent.maxCapacity}`);
          }

          // 更新库存
          await tx.reagent.update({
            where: { id: item.reagentId },
            data: { currentStock: afterStock },
          });

          // 创建交易记录
          const transaction = await tx.stockTransaction.create({
            data: {
              reagentId: item.reagentId,
              userId,
              type: 'IN',
              quantity: item.quantity,
              beforeStock,
              afterStock,
              reason: item.reason,
              batchNumber: item.batchNumber,
              expiryDate: item.expiryDate,
              supplier: item.supplier,
              price: item.price,
              totalPrice: item.price ? item.price * item.quantity : null,
              invoiceNumber: item.invoiceNumber,
              notes: item.notes,
            },
          });

          results.push({
            transactionId: transaction.id,
            reagentId: item.reagentId,
            type: 'IN',
            quantity: item.quantity,
            beforeStock,
            afterStock,
            success: true,
            message: `成功入库 ${item.quantity} ${reagent.unit}`,
          });
        } catch (error) {
          results.push({
            transactionId: '',
            reagentId: item.reagentId,
            type: 'IN',
            quantity: item.quantity,
            beforeStock: 0,
            afterStock: 0,
            success: false,
            message: error instanceof Error ? error.message : '未知错误',
          });
        }
      }

      return results;
    });
  }

  /**
   * 验证试剂存在性
   */
  private async validateReagentExists(tx: any, reagentId: string) {
    const reagent = await tx.reagent.findUnique({
      where: { id: reagentId },
    });

    if (!reagent) {
      throw new Error('试剂不存在');
    }

    if (!reagent.isActive) {
      throw new Error('试剂已停用，无法进行库存操作');
    }

    return reagent;
  }

  /**
   * 检查并创建库存警报
   */
  private async checkAndCreateStockAlert(tx: any, reagent: any, currentStock: number) {
    const minThreshold = Number(reagent.minThreshold);

    if (currentStock <= minThreshold) {
      await tx.stockAlert.create({
        data: {
          reagentId: reagent.id,
          type: currentStock === 0 ? 'OUT_OF_STOCK' : 'LOW_STOCK',
          level: currentStock === 0 ? 'CRITICAL' : 'HIGH',
          message: currentStock === 0
            ? `试剂 ${reagent.name} 已缺货`
            : `试剂 ${reagent.name} 库存不足，当前库存：${currentStock}`,
        },
      });
    }
  }
}
