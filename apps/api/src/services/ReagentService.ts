import { PrismaClient, Reagent, ReagentCategory } from '@prisma/client';
import { ReagentRepository, SearchParams, SearchResult, StockStatus } from '../models/ReagentRepository';

/**
 * 搜索响应接口
 */
export interface SearchResponse {
  reagents: ReagentWithStatus[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
  filters: {
    categories: Record<ReagentCategory, number>;
    popularKeywords: string[];
  };
}

/**
 * 带库存状态的试剂接口
 */
export interface ReagentWithStatus extends Reagent {
  stockStatus: StockStatus;
  lastTransaction?: {
    createdAt: Date;
    type: string;
  };
}

/**
 * 缓存配置
 */
interface CacheConfig {
  searchTTL: number; // 搜索结果缓存时间（秒）
  statsTTL: number;  // 统计数据缓存时间（秒）
}

/**
 * 试剂业务逻辑层
 * 处理复杂的业务逻辑、缓存策略和错误处理
 */
export class ReagentService {
  private repository: ReagentRepository;
  private cache: Map<string, { data: any; expiry: number }> = new Map();
  private cacheConfig: CacheConfig = {
    searchTTL: 300, // 5分钟
    statsTTL: 3600  // 1小时
  };

  constructor(private prisma: PrismaClient) {
    this.repository = new ReagentRepository(prisma);
  }

  /**
   * 搜索试剂（带缓存）
   */
  async search(params: SearchParams): Promise<SearchResponse> {
    try {
      // 生成缓存键
      const cacheKey = this.generateCacheKey('search', params);

      // 检查缓存
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        return cached;
      }

      // 执行搜索
      const searchResult = await this.repository.search(params);

      // 添加库存状态
      const reagentsWithStatus: ReagentWithStatus[] = searchResult.reagents.map(reagent => ({
        ...reagent,
        stockStatus: this.repository.calculateStockStatus(reagent),
        lastTransaction: (reagent as any).stockTransactions?.[0] || undefined
      }));

      // 获取筛选器数据
      const [categoryStats, popularKeywords] = await Promise.all([
        this.getCategoryStats(),
        this.getPopularKeywords()
      ]);

      const response: SearchResponse = {
        reagents: reagentsWithStatus,
        pagination: {
          total: searchResult.total,
          limit: params.limit || 20,
          offset: params.offset || 0,
          hasMore: searchResult.hasMore
        },
        filters: {
          categories: categoryStats,
          popularKeywords
        }
      };

      // 缓存结果
      this.setCache(cacheKey, response, this.cacheConfig.searchTTL);

      return response;
    } catch (error) {
      console.error('搜索试剂时发生错误:', error);
      throw new Error(`搜索试剂失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 根据ID获取试剂详情
   */
  async getById(id: string): Promise<ReagentWithStatus | null> {
    try {
      const cacheKey = `reagent:${id}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        return cached;
      }

      const reagent = await this.repository.findById(id);
      if (!reagent) {
        return null;
      }

      const reagentWithStatus: ReagentWithStatus = {
        ...reagent,
        stockStatus: this.repository.calculateStockStatus(reagent),
        lastTransaction: (reagent as any).stockTransactions?.[0] || undefined
      };

      // 缓存结果（较短时间，因为库存可能变化）
      this.setCache(cacheKey, reagentWithStatus, 60);

      return reagentWithStatus;
    } catch (error) {
      console.error('获取试剂详情时发生错误:', error);
      throw new Error(`获取试剂详情失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取分类统计（带缓存）
   */
  async getCategoryStats(): Promise<Record<ReagentCategory, number>> {
    const cacheKey = 'category:stats';
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return cached;
    }

    const stats = await this.repository.getCategoryStats();
    this.setCache(cacheKey, stats, this.cacheConfig.statsTTL);
    return stats;
  }

  /**
   * 获取热门关键词（带缓存）
   */
  async getPopularKeywords(limit: number = 10): Promise<string[]> {
    const cacheKey = `keywords:popular:${limit}`;
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return cached;
    }

    const keywords = await this.repository.getPopularKeywords(limit);
    this.setCache(cacheKey, keywords, this.cacheConfig.statsTTL);
    return keywords;
  }

  /**
   * 验证搜索参数
   */
  validateSearchParams(params: SearchParams): void {
    if (params.limit !== undefined && (params.limit < 1 || params.limit > 100)) {
      throw new Error('分页大小必须在1-100之间');
    }

    if (params.offset !== undefined && params.offset < 0) {
      throw new Error('偏移量不能为负数');
    }

    if (params.query !== undefined && params.query.length > 100) {
      throw new Error('搜索关键词不能超过100个字符');
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(prefix: string, params: any): string {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key];
        return result;
      }, {} as any);

    return `${prefix}:${JSON.stringify(sortedParams)}`;
  }

  /**
   * 从缓存获取数据
   */
  private getFromCache(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && cached.expiry > Date.now()) {
      return cached.data;
    }

    if (cached) {
      this.cache.delete(key);
    }

    return null;
  }

  /**
   * 设置缓存
   */
  private setCache(key: string, data: any, ttlSeconds: number): void {
    const expiry = Date.now() + (ttlSeconds * 1000);
    this.cache.set(key, { data, expiry });
  }

  /**
   * 清除缓存
   */
  clearCache(pattern?: string): void {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}
