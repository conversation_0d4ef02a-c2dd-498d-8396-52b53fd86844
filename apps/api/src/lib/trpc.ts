import { initTRPC, TRPCError } from '@trpc/server';
import { Context } from '../context';
import { ZodError } from 'zod';

/**
 * 初始化tRPC
 */
const t = initTRPC.context<Context>().create({
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    };
  },
});

/**
 * 导出可重用的路由器和过程构建器
 */
export const router = t.router;
export const publicProcedure = t.procedure;

/**
 * 受保护的过程 - 需要用户认证
 */
export const protectedProcedure = t.procedure.use(({ ctx, next }) => {
  if (!ctx.user) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }
  return next({
    ctx: {
      ...ctx,
      user: ctx.user, // 确保用户已认证
    },
  });
});

/**
 * 管理员过程 - 需要管理员权限
 */
export const adminProcedure = protectedProcedure.use(({ ctx, next }) => {
  if (ctx.user?.role !== 'ADMIN') {
    throw new TRPCError({ code: 'FORBIDDEN' });
  }
  return next({ ctx });
});

/**
 * 管理员或经理过程 - 需要管理员或经理权限
 */
export const managerProcedure = protectedProcedure.use(({ ctx, next }) => {
  if (!['ADMIN', 'MANAGER'].includes(ctx.user?.role || '')) {
    throw new TRPCError({ code: 'FORBIDDEN' });
  }
  return next({ ctx });
});
