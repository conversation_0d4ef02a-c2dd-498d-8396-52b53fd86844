import { inferAsyncReturnType } from '@trpc/server';
import { CreateExpressContextOptions } from '@trpc/server/adapters/express';
import jwt from 'jsonwebtoken';
import { prisma } from './lib/prisma';

/**
 * 创建tRPC上下文
 * 这个上下文会在每个请求中可用
 */
export const createContext = async ({ req, res }: CreateExpressContextOptions) => {
  // 从请求头中获取用户信息（如果有JWT token）
  const token = req.headers.authorization?.replace('Bearer ', '');
  let user = null;

  if (token) {
    try {
      const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
      const decoded = jwt.verify(token, jwtSecret) as { userId: string };

      // 从数据库获取用户信息
      user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          email: true,
          username: true,
          name: true,
          role: true,
          isActive: true,
        },
      });

      // 如果用户不活跃，则不认证
      if (user && !user.isActive) {
        user = null;
      }
    } catch (error) {
      // JWT验证失败，用户保持为null
      console.warn('JWT验证失败:', error);
    }
  }

  return {
    req,
    res,
    prisma,
    user,
    token,
  };
};

export type Context = inferAsyncReturnType<typeof createContext>;
