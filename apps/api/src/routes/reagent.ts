import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, publicProcedure, protectedProcedure, managerProcedure } from '../lib/trpc';
import { ReagentService } from '../services/ReagentService';
import { ReagentCategory } from '@prisma/client';

/**
 * 试剂相关路由
 */
export const reagentRouter = router({
  // 新的搜索API - 支持模糊搜索、筛选、分页和排序
  search: publicProcedure
    .input(z.object({
      query: z.string().optional(),
      category: z.nativeEnum(ReagentCategory).optional(),
      supplier: z.string().optional(),
      lowStock: z.boolean().optional(),
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
      sortBy: z.enum(['name', 'code', 'currentStock', 'createdAt']).default('createdAt'),
      sortOrder: z.enum(['asc', 'desc']).default('desc'),
    }))
    .query(async ({ ctx, input }) => {
      const reagentService = new ReagentService(ctx.prisma);

      // 验证搜索参数
      reagentService.validateSearchParams(input);

      // 执行搜索
      return await reagentService.search(input);
    }),

  // 获取试剂详情（增强版）
  getById: publicProcedure
    .input(z.object({
      id: z.string().uuid(),
    }))
    .query(async ({ ctx, input }) => {
      const reagentService = new ReagentService(ctx.prisma);
      const reagent = await reagentService.getById(input.id);

      if (!reagent) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: '试剂不存在',
        });
      }

      return reagent;
    }),

  // 获取分类统计
  getCategoryStats: publicProcedure
    .query(async ({ ctx }) => {
      const reagentService = new ReagentService(ctx.prisma);
      return await reagentService.getCategoryStats();
    }),

  // 获取热门搜索关键词
  getPopularKeywords: publicProcedure
    .input(z.object({
      limit: z.number().min(1).max(50).default(10),
    }))
    .query(async ({ ctx, input }) => {
      const reagentService = new ReagentService(ctx.prisma);
      return await reagentService.getPopularKeywords(input.limit);
    }),
  // 获取试剂列表（兼容旧版本API）
  list: protectedProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      search: z.string().optional(),
      category: z.enum(['YF03', 'YF04', 'YF06']).optional(),
      isActive: z.boolean().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const reagentService = new ReagentService(ctx.prisma);

      // 转换为新的搜索参数格式
      const searchParams = {
        query: input.search,
        category: input.category ?
          (input.category === 'YF03' ? ReagentCategory.BIOLOGICAL_REAGENT :
            input.category === 'YF04' ? ReagentCategory.LAB_CONSUMABLE :
              ReagentCategory.CULTURE_MEDIUM) : undefined,
        limit: input.limit,
        offset: (input.page - 1) * input.limit,
        sortBy: 'createdAt' as const,
        sortOrder: 'desc' as const,
      };

      const result = await reagentService.search(searchParams);

      // 转换为旧版本响应格式
      return {
        reagents: result.reagents,
        pagination: {
          page: input.page,
          limit: input.limit,
          total: result.pagination.total,
          pages: Math.ceil(result.pagination.total / input.limit),
        },
      };
    }),



  // 创建试剂（管理员/经理权限）
  create: managerProcedure
    .input(z.object({
      code: z.string().min(1),
      name: z.string().min(1),
      specification: z.string().optional(),
      category: z.nativeEnum(ReagentCategory),
      supplier: z.string().optional(),
      manufacturer: z.string().optional(),
      casNumber: z.string().optional(),
      formula: z.string().optional(),
      molecularWeight: z.number().positive().optional(),
      purity: z.string().optional(),
      storageCondition: z.string().optional(),
      safetyLevel: z.string().optional(),
      currentStock: z.number().min(0).default(0),
      minThreshold: z.number().min(0).default(0),
      maxCapacity: z.number().positive().optional(),
      unit: z.string().default('瓶'),
      price: z.number().positive().optional(),
      location: z.string().optional(),
      description: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const reagent = await ctx.prisma.reagent.create({
        data: input,
      });

      return reagent;
    }),

  // 更新试剂（管理员/经理权限）
  update: managerProcedure
    .input(z.object({
      id: z.string(),
      code: z.string().min(1).optional(),
      name: z.string().min(1).optional(),
      specification: z.string().optional(),
      category: z.nativeEnum(ReagentCategory).optional(),
      supplier: z.string().optional(),
      manufacturer: z.string().optional(),
      casNumber: z.string().optional(),
      formula: z.string().optional(),
      molecularWeight: z.number().positive().optional(),
      purity: z.string().optional(),
      storageCondition: z.string().optional(),
      safetyLevel: z.string().optional(),
      minThreshold: z.number().min(0).optional(),
      maxCapacity: z.number().positive().optional(),
      unit: z.string().optional(),
      price: z.number().positive().optional(),
      location: z.string().optional(),
      description: z.string().optional(),
      isActive: z.boolean().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;

      const reagent = await ctx.prisma.reagent.update({
        where: { id },
        data: updateData,
      });

      return reagent;
    }),

  // 删除试剂（管理员/经理权限）
  delete: managerProcedure
    .input(z.object({
      id: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      await ctx.prisma.reagent.delete({
        where: { id: input.id },
      });

      return { success: true };
    }),
});
