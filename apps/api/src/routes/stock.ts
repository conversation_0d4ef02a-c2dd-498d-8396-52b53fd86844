import { z } from 'zod';
import { router, protectedProcedure, managerProcedure } from '../lib/trpc';
import { StockService } from '../services/StockService';

/**
 * 库存相关路由
 */
export const stockRouter = router({
  // 获取库存交易记录
  transactions: protectedProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      reagentId: z.string().optional(),
      type: z.enum(['IN', 'OUT', 'ADJUST', 'RETURN', 'LOSS']).optional(),
      startDate: z.string().optional(),
      endDate: z.string().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { page, limit, reagentId, type, startDate, endDate } = input;
      const skip = (page - 1) * limit;

      const where: any = {};

      if (reagentId) where.reagentId = reagentId;
      if (type) where.type = type;

      if (startDate || endDate) {
        where.createdAt = {};
        if (startDate) where.createdAt.gte = new Date(startDate);
        if (endDate) where.createdAt.lte = new Date(endDate);
      }

      const [transactions, total] = await Promise.all([
        ctx.prisma.stockTransaction.findMany({
          where,
          skip,
          take: limit,
          include: {
            reagent: {
              select: { code: true, name: true, unit: true },
            },
            user: {
              select: { name: true, username: true },
            },
          },
          orderBy: { createdAt: 'desc' },
        }),
        ctx.prisma.stockTransaction.count({ where }),
      ]);

      return {
        transactions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),

  // 入库操作
  stockIn: protectedProcedure
    .input(z.object({
      reagentId: z.string(),
      quantity: z.number().positive(),
      reason: z.string().optional(),
      batchNumber: z.string().optional(),
      expiryDate: z.string().optional(),
      supplier: z.string().optional(),
      price: z.number().positive().optional(),
      invoiceNumber: z.string().optional(),
      notes: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const stockService = new StockService(ctx.prisma);
      const stockInInput = {
        ...input,
        expiryDate: input.expiryDate ? new Date(input.expiryDate) : undefined,
      };

      const result = await stockService.stockIn(stockInInput, ctx.user!.id);

      // 返回事务记录格式以保持API兼容性
      return {
        id: result.transactionId,
        reagentId: result.reagentId,
        type: result.type,
        quantity: result.quantity,
        beforeStock: result.beforeStock,
        afterStock: result.afterStock,
      };
    }),

  // 出库操作
  stockOut: protectedProcedure
    .input(z.object({
      reagentId: z.string(),
      quantity: z.number().positive(),
      reason: z.string().optional(),
      notes: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const stockService = new StockService(ctx.prisma);
      const result = await stockService.stockOut(input, ctx.user!.id);

      // 返回事务记录格式以保持API兼容性
      return {
        id: result.transactionId,
        reagentId: result.reagentId,
        type: result.type,
        quantity: -result.quantity, // 保持原有API格式（负数表示出库）
        beforeStock: result.beforeStock,
        afterStock: result.afterStock,
      };
    }),

  // 库存调整操作 (仅管理员)
  adjust: managerProcedure
    .input(z.object({
      reagentId: z.string(),
      newQuantity: z.number().min(0),
      reason: z.string().min(1, '调整原因不能为空'),
      notes: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const stockService = new StockService(ctx.prisma);
      const result = await stockService.adjust(input, ctx.user!.id);

      // 返回事务记录格式以保持API兼容性
      return {
        id: result.transactionId,
        reagentId: result.reagentId,
        type: result.type,
        quantity: result.afterStock - result.beforeStock, // 计算实际变化量
        beforeStock: result.beforeStock,
        afterStock: result.afterStock,
      };
    }),

  // 批量入库操作
  batchStockIn: protectedProcedure
    .input(z.object({
      items: z.array(z.object({
        reagentId: z.string(),
        quantity: z.number().positive(),
        reason: z.string().optional(),
        batchNumber: z.string().optional(),
        expiryDate: z.string().optional(),
        supplier: z.string().optional(),
        price: z.number().positive().optional(),
        invoiceNumber: z.string().optional(),
        notes: z.string().optional(),
      })).min(1).max(50), // 限制批量操作数量
    }))
    .mutation(async ({ ctx, input }) => {
      const stockService = new StockService(ctx.prisma);

      // 转换输入格式
      const batchInput = {
        items: input.items.map(item => ({
          ...item,
          expiryDate: item.expiryDate ? new Date(item.expiryDate) : undefined,
        })),
      };

      const results = await stockService.batchStockIn(batchInput, ctx.user!.id);

      // 转换为API兼容格式
      return results.map(result => ({
        id: result.transactionId,
        reagentId: result.reagentId,
        type: result.type,
        quantity: result.quantity,
        beforeStock: result.beforeStock,
        afterStock: result.afterStock,
        success: result.success,
        message: result.message,
      }));
    }),

  // 获取库存历史记录
  getHistory: protectedProcedure
    .input(z.object({
      reagentId: z.string(),
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
    }))
    .query(async ({ ctx, input }) => {
      const { reagentId, page, limit } = input;
      const skip = (page - 1) * limit;

      const [transactions, total] = await Promise.all([
        ctx.prisma.stockTransaction.findMany({
          where: { reagentId },
          skip,
          take: limit,
          include: {
            user: {
              select: { name: true, username: true },
            },
          },
          orderBy: { createdAt: 'desc' },
        }),
        ctx.prisma.stockTransaction.count({ where: { reagentId } }),
      ]);

      return {
        transactions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),
});
