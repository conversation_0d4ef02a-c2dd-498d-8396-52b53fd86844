import { z } from 'zod';
import { router, protectedProcedure, managerProcedure } from '../lib/trpc';

/**
 * 警报相关路由
 */
export const alertRouter = router({
  // 获取警报列表
  list: protectedProcedure
    .input(z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20),
      type: z.enum(['LOW_STOCK', 'EXPIRY_SOON', 'EXPIRED', 'OUT_OF_STOCK']).optional(),
      level: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).optional(),
      isRead: z.boolean().optional(),
      isResolved: z.boolean().optional(),
    }))
    .query(async ({ ctx, input }) => {
      const { page, limit, type, level, isRead, isResolved } = input;
      const skip = (page - 1) * limit;

      const where: any = {};

      if (type) where.type = type;
      if (level) where.level = level;
      if (isRead !== undefined) where.isRead = isRead;
      if (isResolved !== undefined) where.isResolved = isResolved;

      const [alerts, total] = await Promise.all([
        ctx.prisma.stockAlert.findMany({
          where,
          skip,
          take: limit,
          include: {
            reagent: {
              select: { code: true, name: true, currentStock: true, minThreshold: true },
            },
            user: {
              select: { name: true, username: true },
            },
          },
          orderBy: [
            { level: 'desc' },
            { createdAt: 'desc' },
          ],
        }),
        ctx.prisma.stockAlert.count({ where }),
      ]);

      return {
        alerts,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    }),

  // 获取警报统计
  stats: protectedProcedure
    .query(async ({ ctx }) => {
      const [
        totalAlerts,
        unreadAlerts,
        unresolvedAlerts,
        criticalAlerts,
        lowStockAlerts,
        outOfStockAlerts,
      ] = await Promise.all([
        ctx.prisma.stockAlert.count(),
        ctx.prisma.stockAlert.count({ where: { isRead: false } }),
        ctx.prisma.stockAlert.count({ where: { isResolved: false } }),
        ctx.prisma.stockAlert.count({
          where: { level: 'CRITICAL', isResolved: false }
        }),
        ctx.prisma.stockAlert.count({
          where: { type: 'LOW_STOCK', isResolved: false }
        }),
        ctx.prisma.stockAlert.count({
          where: { type: 'OUT_OF_STOCK', isResolved: false }
        }),
      ]);

      return {
        total: totalAlerts,
        unread: unreadAlerts,
        unresolved: unresolvedAlerts,
        critical: criticalAlerts,
        lowStock: lowStockAlerts,
        outOfStock: outOfStockAlerts,
      };
    }),

  // 标记警报为已读
  markAsRead: protectedProcedure
    .input(z.object({
      id: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const alert = await ctx.prisma.stockAlert.update({
        where: { id: input.id },
        data: { isRead: true },
      });

      return alert;
    }),

  // 批量标记警报为已读
  markMultipleAsRead: protectedProcedure
    .input(z.object({
      ids: z.array(z.string()),
    }))
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.prisma.stockAlert.updateMany({
        where: { id: { in: input.ids } },
        data: { isRead: true },
      });

      return { count: result.count };
    }),

  // 解决警报
  resolve: managerProcedure
    .input(z.object({
      id: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const alert = await ctx.prisma.stockAlert.update({
        where: { id: input.id },
        data: {
          isResolved: true,
          resolvedAt: new Date(),
          userId: ctx.user!.id,
        },
      });

      return alert;
    }),

  // 创建手动警报
  create: managerProcedure
    .input(z.object({
      reagentId: z.string(),
      type: z.enum(['LOW_STOCK', 'EXPIRY_SOON', 'EXPIRED', 'OUT_OF_STOCK']),
      level: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
      message: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const alert = await ctx.prisma.stockAlert.create({
        data: input,
      });

      return alert;
    }),
});
