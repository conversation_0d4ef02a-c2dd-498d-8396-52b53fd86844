import { PrismaClient, Reagent, ReagentCategory } from '@prisma/client';

/**
 * 搜索参数接口
 */
export interface SearchParams {
  query?: string;
  category?: ReagentCategory;
  supplier?: string;
  lowStock?: boolean;
  limit?: number;
  offset?: number;
  sortBy?: 'name' | 'code' | 'currentStock' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

/**
 * 搜索结果接口
 */
export interface SearchResult {
  reagents: Reagent[];
  total: number;
  hasMore: boolean;
}

/**
 * 库存状态枚举
 */
export enum StockStatus {
  OUT_OF_STOCK = 'OUT_OF_STOCK',
  LOW_STOCK = 'LOW_STOCK',
  IN_STOCK = 'IN_STOCK'
}

/**
 * 试剂数据访问层
 * 负责与数据库的直接交互和查询优化
 */
export class ReagentRepository {
  constructor(private prisma: PrismaClient) { }

  /**
   * 搜索试剂
   * 支持模糊搜索、筛选、分页和排序
   */
  async search(params: SearchParams): Promise<SearchResult> {
    const {
      query,
      category,
      supplier,
      lowStock,
      limit = 20,
      offset = 0,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = params;

    // 构建查询条件
    const where: Record<string, any> = {
      isActive: true, // 只查询活跃的试剂
    };

    // 模糊搜索：名称、编码、规格
    if (query && query.trim()) {
      where.OR = [
        { name: { contains: query.trim(), mode: 'insensitive' } },
        { code: { contains: query.trim(), mode: 'insensitive' } },
        { specification: { contains: query.trim(), mode: 'insensitive' } },
      ];
    }

    // 分类筛选
    if (category) {
      where.category = category;
    }

    // 供应商筛选
    if (supplier && supplier.trim()) {
      where.supplier = { contains: supplier.trim(), mode: 'insensitive' };
    }

    // 低库存筛选 - 使用原生SQL查询
    if (lowStock) {
      // 使用原生查询来比较 currentStock 和 minThreshold
      const lowStockReagents = await this.prisma.$queryRaw<{ id: string }[]>`
        SELECT id FROM "Reagent"
        WHERE "isActive" = true
        AND ("currentStock" <= 0 OR "currentStock" <= "minThreshold")
      `;

      const lowStockIds = lowStockReagents.map(r => r.id);
      if (lowStockIds.length > 0) {
        where.id = { in: lowStockIds };
      } else {
        // 如果没有低库存试剂，返回空结果
        where.id = { in: [] };
      }
    }

    // 排序配置
    const orderBy: Record<string, 'asc' | 'desc'> = {};
    orderBy[sortBy] = sortOrder;

    // 执行查询
    const [reagents, total] = await Promise.all([
      this.prisma.reagent.findMany({
        where,
        skip: offset,
        take: limit,
        orderBy,
        include: {
          stockTransactions: {
            take: 1,
            orderBy: { createdAt: 'desc' },
            select: { createdAt: true, type: true }
          }
        }
      }),
      this.prisma.reagent.count({ where })
    ]);

    return {
      reagents,
      total,
      hasMore: offset + limit < total
    };
  }

  /**
   * 根据ID获取试剂详情
   */
  async findById(id: string): Promise<Reagent | null> {
    return this.prisma.reagent.findUnique({
      where: { id },
      include: {
        stockTransactions: {
          take: 10,
          orderBy: { createdAt: 'desc' },
          include: {
            user: {
              select: { name: true, username: true }
            }
          }
        },
        stockAlerts: {
          where: { isResolved: false },
          orderBy: { createdAt: 'desc' }
        }
      }
    });
  }

  /**
   * 计算库存状态
   */
  calculateStockStatus(reagent: Reagent): StockStatus {
    const currentStock = Number(reagent.currentStock);
    const minThreshold = Number(reagent.minThreshold);

    if (currentStock <= 0) {
      return StockStatus.OUT_OF_STOCK;
    } else if (currentStock <= minThreshold) {
      return StockStatus.LOW_STOCK;
    } else {
      return StockStatus.IN_STOCK;
    }
  }

  /**
   * 获取热门搜索关键词
   * 基于试剂名称和供应商的统计
   */
  async getPopularKeywords(limit: number = 10): Promise<string[]> {
    // 这里可以实现基于搜索日志的热门关键词
    // 目前返回常见的供应商和分类
    const suppliers = await this.prisma.reagent.groupBy({
      by: ['supplier'],
      where: {
        isActive: true,
        supplier: { not: null }
      },
      _count: { supplier: true },
      orderBy: { _count: { supplier: 'desc' } },
      take: limit
    });

    return suppliers
      .map(s => s.supplier)
      .filter(Boolean) as string[];
  }

  /**
   * 获取分类统计
   */
  async getCategoryStats(): Promise<Record<ReagentCategory, number>> {
    const stats = await this.prisma.reagent.groupBy({
      by: ['category'],
      where: { isActive: true },
      _count: { category: true }
    });

    const result: Record<ReagentCategory, number> = {
      [ReagentCategory.BIOLOGICAL_REAGENT]: 0,
      [ReagentCategory.LAB_CONSUMABLE]: 0,
      [ReagentCategory.CULTURE_MEDIUM]: 0
    };

    stats.forEach(stat => {
      result[stat.category] = stat._count.category;
    });

    return result;
  }
}
