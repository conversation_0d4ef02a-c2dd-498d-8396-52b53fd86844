{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "commonjs", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "composite": true, "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/routes/*": ["./src/routes/*"], "@/services/*": ["./src/services/*"], "@/models/*": ["./src/models/*"], "@haocai/shared/*": ["../../packages/shared/src/*"], "@haocai/config/*": ["../../packages/config/src/*"]}, "types": ["node", "jest"], "typeRoots": ["../../node_modules/@types", "./node_modules/@types"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests", "**/*.test.ts"], "ts-node": {"esm": true}}