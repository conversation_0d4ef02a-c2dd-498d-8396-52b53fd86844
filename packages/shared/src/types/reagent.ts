/**
 * 试剂相关类型定义
 */

/**
 * 试剂分类类型
 */
export type ReagentCategory = 'BIOLOGICAL_REAGENT' | 'LAB_CONSUMABLE' | 'CULTURE_MEDIUM';

/**
 * 库存状态枚举
 */
export enum StockStatus {
  OUT_OF_STOCK = 'OUT_OF_STOCK',
  LOW_STOCK = 'LOW_STOCK',
  IN_STOCK = 'IN_STOCK'
}

/**
 * 试剂基础接口
 */
export interface Reagent {
  id: string;
  code: string;
  name: string;
  specification: string | null;
  category: ReagentCategory;
  supplier: string | null;
  manufacturer: string | null;
  casNumber: string | null;
  formula: string | null;
  molecularWeight: string | number | null;
  purity: string | null;
  storageCondition: string | null;
  safetyLevel: string | null;
  currentStock: string | number;
  minThreshold: string | number;
  maxCapacity: string | number | null;
  unit: string;
  unitPrice: string | number | null;
  totalPrice: string | number | null;
  location: string | null;
  description: string | null;
  isActive: boolean;
  createdAt: string | Date;
  updatedAt: string | Date;
  createdBy: string | null;
  updatedBy: string | null;
}

/**
 * 带库存状态的试剂接口
 */
export interface ReagentWithStatus extends Reagent {
  stockStatus: StockStatus;
  lastTransaction?: {
    createdAt: string | Date;
    type: string;
  };
}

/**
 * 搜索参数接口
 */
export interface SearchParams {
  query?: string;
  category?: string;
  supplier?: string;
  lowStock?: boolean;
  limit?: number;
  offset?: number;
  sortBy?: 'name' | 'code' | 'currentStock' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

/**
 * 搜索结果接口
 */
export interface SearchResult {
  reagents: ReagentWithStatus[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
  filters: {
    categories: Record<string, number>;
    popularKeywords: string[];
  };
}

/**
 * 分类显示名称映射
 */
export const CATEGORY_LABELS: Record<string, string> = {
  'BIOLOGICAL_REAGENT': '生物试剂',
  'LAB_CONSUMABLE': '实验耗材',
  'CULTURE_MEDIUM': '培养基'
};

/**
 * 库存状态显示名称映射
 */
export const STOCK_STATUS_LABELS: Record<StockStatus, string> = {
  [StockStatus.OUT_OF_STOCK]: '缺货',
  [StockStatus.LOW_STOCK]: '低库存',
  [StockStatus.IN_STOCK]: '有库存'
};

/**
 * 库存状态颜色映射
 */
export const STOCK_STATUS_COLORS: Record<StockStatus, string> = {
  [StockStatus.OUT_OF_STOCK]: 'red',
  [StockStatus.LOW_STOCK]: 'orange',
  [StockStatus.IN_STOCK]: 'green'
};

/**
 * 排序字段显示名称映射
 */
export const SORT_FIELD_LABELS: Record<string, string> = {
  name: '名称',
  code: '编码',
  currentStock: '库存量',
  createdAt: '创建时间'
};
