/**
 * API 相关类型定义
 */

import { ReagentWithStatus, SearchParams, SearchResult } from './reagent';

/**
 * API 响应基础接口
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
}

/**
 * 分页响应接口
 */
export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

/**
 * 错误响应接口
 */
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

/**
 * tRPC 路由器类型定义
 * 这里定义前端需要的 API 接口类型，而不是直接导入后端路由器
 */
export interface AppRouterType {
  reagent: {
    search: {
      input: SearchParams;
      output: SearchResult;
    };
    getById: {
      input: { id: string };
      output: ReagentWithStatus | null;
    };
    create: {
      input: Omit<ReagentWithStatus, 'id' | 'createdAt' | 'updatedAt' | 'stockStatus'>;
      output: ReagentWithStatus;
    };
    update: {
      input: { id: string } & Partial<Omit<ReagentWithStatus, 'id' | 'createdAt' | 'updatedAt' | 'stockStatus'>>;
      output: ReagentWithStatus;
    };
    delete: {
      input: { id: string };
      output: { success: boolean };
    };
  };
  user: {
    me: {
      input: void;
      output: {
        id: string;
        email: string;
        name: string;
        role: string;
      } | null;
    };
    login: {
      input: {
        email: string;
        password: string;
      };
      output: {
        user: {
          id: string;
          email: string;
          name: string;
          role: string;
        };
        token: string;
      };
    };
    logout: {
      input: void;
      output: { success: boolean };
    };
  };
}

/**
 * API 端点常量
 */
export const API_ENDPOINTS = {
  TRPC: '/api/trpc',
  HEALTH: '/api/health',
  AUTH: '/api/auth',
} as const;

/**
 * HTTP 状态码常量
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

/**
 * API 错误代码常量
 */
export const API_ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
} as const;
