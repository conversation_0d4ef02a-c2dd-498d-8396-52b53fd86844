{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "commonjs", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "composite": true, "removeComments": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}